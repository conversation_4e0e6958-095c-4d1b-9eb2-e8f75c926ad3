<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father type="heroSkill" cnName="英雄技能所需">
		<bullet cnName="万弹归宗">
			<name>moreMissile_hero</name>
			<cnName>万弹归宗</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0.75</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>10</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>180</bulletAngle>
			<bulletNum>10</bulletNum>				
			<shootNum>1</shootNum>					
			<shootAngle>60</shootAngle>					
			<!--特殊属性------------------------------------------------------------ -->	
			<followD value="2" delay="0.1" />	 跟踪 -->
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-30,-60</shootPoint>
			<bulletSpeed>15</bulletSpeed>
			<speedD random="0.3" />
			<gravity>0</gravity>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">bullet/missile_bullet</bulletImgUrl>
			<hitImgUrl  soundUrl="boomSound/microBoom2">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="万弹归宗-眩晕">
			<name>moreMissile_hero_dizziness</name>
			<cnName>万弹归宗</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0.75</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>10</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>180</bulletAngle>
			<bulletNum>10</bulletNum>				
			<shootNum>1</shootNum>					
			<shootAngle>60</shootAngle>					
			<!--特殊属性------------------------------------------------------------ -->	
			<followD value="2" delay="0.1" />	 跟踪 -->
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-30,-60</shootPoint>
			<bulletSpeed>15</bulletSpeed>
			<speedD random="0.3" />
			<skillArr>moreMissile_hero_dizziness</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">bullet/missile_bullet</bulletImgUrl>
			<hitImgUrl  soundUrl="boomSound/microBoom2">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="万弹归宗-眩晕">
			<name>moreMissile_hero_dizziness2</name>
			<cnName>万弹归宗</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0.75</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>3</bulletLife>
			<bulletWidth>10</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>180</bulletAngle>
			<bulletNum>10</bulletNum>				
			<shootNum>1</shootNum>					
			<shootAngle>60</shootAngle>					
			<!--特殊属性------------------------------------------------------------ -->	
			<followD value="2" delay="0.1" />	 跟踪 -->
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-30,-60</shootPoint>
			<bulletSpeed>20</bulletSpeed>
			<speedD random="0.3" />
			<skillArr>moreMissile_hero_dizziness2</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">bullet/missile_bullet</bulletImgUrl>
			<hitImgUrl  soundUrl="boomSound/microBoom2">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		
		
		<bullet cnName="派生导弹">
			<name>hitMissile_hero</name>
			<cnName>派生导弹</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>3</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>0</bulletAngle>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>					
								
			<shootAngle>0</shootAngle>					
			<!--特殊属性------------------------------------------------------------ -->	
			<followD value="1" delay="0.2" />	<!-- 跟踪 -->
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,0</shootPoint>
			<bulletSpeed>25</bulletSpeed>
			<speedD random="0.1"/>
			<gravity>0</gravity>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">bullet/missile_bullet</bulletImgUrl>
			<hitImgUrl  name="LastdayBigChild_hit"/>
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="派生导弹-套件">
			<name>hitMissile_outfit</name>
			<cnName>派生导弹-套件</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0.2</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>0</bulletAngle>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>					
								
			<shootAngle>0</shootAngle>					
			<!--特殊属性------------------------------------------------------------ -->	
			<followD value="1" delay="0.1" maxTime="0.7" />	<!-- 跟踪 -->
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,0</shootPoint>
			<bulletSpeed>20</bulletSpeed>
			<speedD random="0.1"/>
			<gravity>0</gravity>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">bullet/laser</bulletImgUrl>
			<hitImgUrl  name="LastdayBigChild_hit"/>
			<smokeImgUrl con="filter" raNum="30">bullet/laser</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		
		
		
		<bullet cnName="定点轰炸">
			<name>pointBoom_hero</name>
			<cnName>定点轰炸</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1.5</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>0.001</bulletLife>
			<bulletWidth>0</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>270</bulletAngle>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>					
								
			<shootAngle>0</shootAngle>					
			<!--特殊属性------------------------------------------------------------ -->	
			<boomD selfB="1" radius="160"/>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,0</shootPoint>
			<bulletSpeed>0</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">bullet/missile_bullet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet cnName="连锁轰炸">
			<name>chainMissile_hero</name>
			<cnName>连锁轰炸</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1.1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>1</bulletLife>
			<bulletWidth>20</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>270</bulletAngle>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>					
								
			<shootAngle>0</shootAngle>					
			<!--特殊属性------------------------------------------------------------ -->	
			<twoHitGap>1</twoHitGap>
			<bounceD body="30" liveInitB="1"/>
			<followD value="2"/>	<!-- 跟踪 -->
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,0</shootPoint>
			<bulletSpeed>20</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">bullet/missile_bullet</bulletImgUrl>
			<hitImgUrl  soundUrl="boomSound/microBoom2">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="无尽轰炮">
			<name>endlessRocket</name>
			<cnName>无尽轰炮</cnName>
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>20</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>5</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>179</bulletAngle>
			<bulletNum>1</bulletNum>				
			<shootNum>50</shootNum>					
			<shootGap>0.1</shootGap>
			<!--特殊属性------------------------------------------------------------ -->	
			<followD value="1"/>	<!-- 跟踪 -->
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,-50</shootPoint>
			<bulletSpeed>30</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30" soundUrl="sound/endlessRocketShoot" volume="0.5">bullet/missile_bullet</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom2"  soundVolume="0.3"  shake="3,0.4,13">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<hitFloorImgUrl soundUrl="boomSound/microBoom" soundVolume="0.5">boomEffect/boom1</hitFloorImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
	</father>
	
	<father type="enemySuperSkill" cnName="精英怪技能所需">
		<bullet cnName="电球爆发">
			<name>electricBoom_enemy</name>
			<cnName>电球爆发</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0.8</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>20</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>230</bulletAngle>
			<bulletAngleRange>60</bulletAngleRange>
			<bulletNum>5</bulletNum>				
			<shootNum>1</shootNum>					
			<shootAngle>60</shootAngle>					
			<extendGap>50</extendGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,-60</shootPoint>
			<bulletSpeed>30</bulletSpeed>
			<speedD random="0.3"/>
			<gravity>0.8</gravity>
			<!--特殊属性------------------------------------------------------------ -->	
			<bounceD floor="1" num="0.8" />	<!-- 反弹 -->
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<skillArr>paralysis_enemy_link</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<bulletImgUrl con="add">skillEffect/paralysis_enemy_bullet</bulletImgUrl>
			<hitImgUrl  soundUrl="sound/paralysis_enemy_hit"></hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">skillEffect/paralysis_enemy_bullet</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="爆石">
			<name>imploding_enemy</name>
			<cnName>爆石</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>20</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>230</bulletAngle>
			<bulletAngleRange>60</bulletAngleRange>
			<bulletNum>5</bulletNum>				
			<shootNum>1</shootNum>					
			<shootAngle>60</shootAngle>					
			<extendGap>50</extendGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,-60</shootPoint>
			<bulletSpeed>30</bulletSpeed>
			<speedD random="0.3"/>
			<gravity>0.8</gravity>
			<!--特殊属性------------------------------------------------------------ -->	
			<bounceD floor="1" num="0.8" />	<!-- 反弹 -->
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl>skillEffect/imploding_enemy_bullet</bulletImgUrl>
			<hitImgUrl  soundUrl="boomSound/smallBoom">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="爆石-幅度小">
			<name>implodingEffect</name>
			<cnName>爆石</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio><noHitB>1</noHitB>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>20</shakeAngle>
			<bulletLife>0.6</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletAngle>230</bulletAngle>
			<bulletNum>5</bulletNum>				
			<shootNum>1</shootNum>					
			<shootAngle>60</shootAngle>					
			<extendGap>20</extendGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-65,-1</shootPoint>
			<bulletSpeed>10</bulletSpeed>
			<speedD random="0.3"/>
			<gravity>0.8</gravity>
			<penetrationGap>1000</penetrationGap>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl>skillEffect/imploding_enemy_bullet</bulletImgUrl>
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="爆石-幅度大">
			<name>implodingEffect2</name>
			<cnName>爆石</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio><noHitB>1</noHitB>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>20</shakeAngle>
			<bulletLife>0.6</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>230</bulletAngle>
			<bulletAngleRange>60</bulletAngleRange>
			<bulletNum>5</bulletNum>				
			<shootNum>1</shootNum>					
			<shootAngle>60</shootAngle>					
			<extendGap>50</extendGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0</shootPoint>
			<bulletSpeed>30</bulletSpeed>
			<speedD random="0.3"/>
			<gravity>0.8</gravity>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl>skillEffect/imploding_enemy_bullet</bulletImgUrl>
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		
		<bullet cnName="爆石-尸宠-伤害较大">
			<name>imploding_pet</name>
			<cnName>爆石</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>2</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>20</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>230</bulletAngle>
			<bulletAngleRange>60</bulletAngleRange>
			<bulletNum>5</bulletNum>				
			<shootNum>1</shootNum>					
			<shootAngle>60</shootAngle>					
			<extendGap>50</extendGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,-60</shootPoint>
			<bulletSpeed>30</bulletSpeed>
			<speedD random="0.3"/>
			<gravity>0.8</gravity>
			<!--特殊属性------------------------------------------------------------ -->	
			<bounceD floor="1" num="0.8" />	<!-- 反弹 -->
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl>skillEffect/imploding_enemy_bullet</bulletImgUrl>
			<hitImgUrl  soundUrl="boomSound/smallBoom">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="爆石-武器技能">
			<name>imploding_godArmsSkill</name>
			<cnName>爆石</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>2</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>20</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>230</bulletAngle>
			<bulletAngleRange>60</bulletAngleRange>
			<bulletNum>5</bulletNum>				
			<shootNum>1</shootNum>					
			<shootAngle>60</shootAngle>					
			<extendGap>50</extendGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,-60</shootPoint>
			<bulletSpeed>30</bulletSpeed>
			<speedD random="0.3"/>
			<gravity>0.8</gravity>
			<!--特殊属性------------------------------------------------------------ -->	
			<bounceD floor="1" num="0.8" />	<!-- 反弹 -->
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl>skillEffect/imploding_enemy_bullet</bulletImgUrl>
			<hitImgUrl  soundUrl="boomSound/microBoom">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="闪电麻痹">
			<name>paralysis_enemy</name>
			<cnName>闪电麻痹</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0.1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>3</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>180</bulletAngle>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>				
			
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,-80</shootPoint>
			<bulletSpeed>15</bulletSpeed>
			<!--特殊属性------------------------------------------------------------ -->	
			<followD value="1" maxTime="2" />	<!-- 跟踪 -->
			<skillArr>paralysis_enemy_link</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="add">skillEffect/paralysis_enemy_bullet</bulletImgUrl>
			<hitImgUrl  soundUrl="sound/paralysis_enemy_hit"></hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet cnName="闪电麻痹-尸宠">
			<name>paralysis_pet</name>
			<cnName>闪电麻痹-尸宠</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>2</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>3</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>180</bulletAngle>
			<bulletNum>3</bulletNum>				
			<shootNum>1</shootNum>					
			<shootAngle>60</shootAngle>					
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,-80</shootPoint>
			<bulletSpeed>15</bulletSpeed>
			<!--特殊属性------------------------------------------------------------ -->	
			<followD value="1" maxTime="2" />	<!-- 跟踪 -->
			<skillArr>paralysis_enemy_link</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="add">skillEffect/paralysis_enemy_bullet</bulletImgUrl>
			<hitImgUrl  soundUrl="sound/paralysis_enemy_hit"></hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
		<bullet cnName="生命置换">
			<name>lifeReplace_enemy</name>
			<cnName>生命置换</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>10</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>180</bulletAngle>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>					
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,-80</shootPoint>
			<bulletSpeed>15</bulletSpeed>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<followD value="1"/>	<!-- 跟踪 -->
			<skillArr>lifeReplace_enemy_link</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="filter">skillEffect/lifeReplace_enemy_bullet</bulletImgUrl>
			<hitImgUrl  soundUrl="sound/lifeReplace_enemy_hit"></hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">skillEffect/poisonClaw_enemy</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="眩晕之锤">
			<name>hammer_enemy</name>
			<cnName>眩晕之锤</cnName><noMagneticB>1</noMagneticB><noBeClearB>1</noBeClearB>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtMul>0.1</hurtMul>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>4.5</bulletLife>
			<bulletWidth>20</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>230</bulletAngle>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>					
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,-70</shootPoint>
			<bulletSpeed>12</bulletSpeed>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<followD value="0.6"/>	<!-- 跟踪 -->
			<skillArr>hammer_enemy_link</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl>skillEffect/hammer_bullet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/hummer_hit">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="技能复制">
			<name>skillCopy_enemy</name>
			<cnName>技能复制</cnName><noMagneticB>1</noMagneticB><noBeClearB>1</noBeClearB>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>10</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>180</bulletAngle>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>					
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,-80</shootPoint>
			<bulletSpeed>15</bulletSpeed>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<followD value="1" maxTime="5" />	<!-- 跟踪 -->
			<skillArr>skillCopy_enemy_link</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="filter">skillEffect/skillCopy_enemy_bullet</bulletImgUrl>
			<hitImgUrl  soundUrl="sound/lifeReplace_enemy_hit"></hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">skillEffect/poisonClaw_enemy</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		
		<bullet cnName="毒蛛-反扑">
			<name>pounce_spider</name>
			<cnName>毒蛛-反扑</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>3</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>3</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>-90</bulletAngle>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>					
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,0</shootPoint>
			<bulletSpeed>2</bulletSpeed>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<followD value="1" maxTime="2"/>	<!-- 跟踪 -->
			<speedD random="0.3" max="20" min="3" a="10" />
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="add">skillEffect/feedback_enemy_bullet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/magicHit1">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
	</father>	
	
	<father type="otherSkill" cnName="其他技能所需">	
		<bullet cnName="电光螺旋丸">
			<name>xiaoBoShoot</name>
			<cnName>小波飞雷神</cnName>
			<hurtMul>0.12</hurtMul>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>2</bulletLife>
			<bulletWidth>90</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletAngle>180</bulletAngle>
			<bulletNum>1</bulletNum>				
			<!--运动属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<shootPoint>-50,0</shootPoint>
			<bulletSpeed>50</bulletSpeed>
			<beatBack>15</beatBack>
			<targetShakeValue>20</targetShakeValue>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1" con="add">skillEffect/stickShadowLeft</bulletImgUrl>
			<bulletLeftImgUrl raNum="1" con="add">skillEffect/stickShadowRight</bulletLeftImgUrl>
			<hitImgUrl soundUrl="sound/body_hit">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="1">skillEffect/stickShadowSmoke</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		
		<bullet cnName="鸣人-疾风斩">
			<name>knife_xiaoMing</name>
			<cnName>鸣人疾风斩</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>0.01</bulletLife>
			<bulletWidth>35</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<twoHitGap>0.5</twoHitGap>
			<twoHitSameNameB>1</twoHitSameNameB>
			<attackGap>0</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>270</bulletAngle>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>					
								
			<shootAngle>0</shootAngle>					
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>999</penetrationNum>
			<skillArr>seckillNormalEnemy</skillArr>
			
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,0</shootPoint>
			<bulletSpeed>0</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<imgClearDelay>0.18</imgClearDelay>
			<bulletImgUrl con="add">skillEffect/piercingLeft</bulletImgUrl>
			<secondBulletImgUrl con="add">skillEffect/piercingRight</secondBulletImgUrl>
			<hitImgUrl>bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet cnName="大Boss群狼">
			<name>summonWolf_bigBoss</name>
			<cnName>大Boss群狼</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0.5</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>179.9</bulletAngle>
			<shakeAngle>0</shakeAngle>
			<bulletLife>4</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.8</attackGap>
			<attackDelay>0.3</attackDelay>
			<bulletNum>1</bulletNum>				
			<shootNum>13</shootNum>					
			<shootGap>0.05</shootGap>					
			<shootAngle>0</shootAngle>					
			<positionD specialType="summonWolf_FightWolf"/>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>5</penetrationNum>
			<twoHitGap>0.5</twoHitGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,0</shootPoint>
			<bulletSpeed>35</bulletSpeed>
			<speedD random="0.2"/>
			<gravity>0</gravity>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1" con="add">generalEffect/wolfSummonBullet</bulletImgUrl>
			<bulletLeftImgUrl raNum="1" con="add">generalEffect/wolfSummonBullet2</bulletLeftImgUrl>
			<hitImgUrl soundUrl="sound/vehicle_hit1" soundVolume="1">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet cnName="武器技能-连弩">
			<name>combo_crossbow</name>
			<cnName>武器技能-连弩</cnName>
			<hurtRatio>1</hurtRatio><armsType>crossbow</armsType>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>2</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>10</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackDelay>0</attackDelay>
			<bulletAngle>179</bulletAngle>
			<bulletNum>1</bulletNum>				
			<!--特殊属性------------------------------------------------------------ -->	
			<bulletSpeed>40</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30" con="add" soundUrl="specialGun/arrowAttack" volume="0.5">specialGun/beadBullet</bulletImgUrl>
			<hitImgUrl soundUrl="specialGun/arrowImpact">bulletHitEffect/yellow_motion</hitImgUrl><!-- 子弹图像【必备】 -->
			<hitFloorImgUrl>bulletHitEffect/yellow_motion</hitFloorImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet cnName="影灭">
			<name>laserKill_godArmsSkill</name>
			<cnName>影灭</cnName><armsType>sniper</armsType>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0.25</hurtRatio>
			<twoHitGap>0.5</twoHitGap>
			<twoHitSameNameB>1</twoHitSameNameB>
			<!--基本属性------------------------------------------------------------ -->
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<bulletNum>2</bulletNum>
			<shootAngle>10</shootAngle>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>39</bulletSpeed>
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>999</penetrationNum>
			<followD value="1" delay="0.1" />
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="filter" raNum="30">bullet/laser2</bulletImgUrl>
			<hitImgUrl soundUrl="sound/magicHit2">bulletHitEffect/energyPurple</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet cnName="沙金-武器技能">
			<name>imploding_blackArmsSkill</name>
			<cnName>沙金</cnName><armsType>shotgun</armsType>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>8</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>190</bulletAngle>
			<bulletNum>4</bulletNum>				
			<shootNum>1</shootNum>					
			<shootAngle>20</shootAngle>					
			<extendGap>50</extendGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,-60</shootPoint>
			<bulletSpeed>20</bulletSpeed>
			<speedD random="0.3"/>
			<gravity>0.4</gravity>
			<!--特殊属性------------------------------------------------------------ -->	
			<bounceD floor="2" num="0.8" />	<!-- 反弹 -->
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="filter" raNum="90">bullet/laserOrange</bulletImgUrl>
			<hitImgUrl soundUrl="sound/magicHit2">bulletHitEffect/energyYellow</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet cnName="青蜂-飞镰">
			<name>sickle_godArmsSkill</name>
			<cnName>青蜂-飞镰</cnName><armsType>rifle</armsType>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>8</shakeAngle>
			<bulletLife>4</bulletLife>
			<bulletWidth>20</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<extendGap>50</extendGap>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletAngle>0</bulletAngle>
			<bulletSpeed>25</bulletSpeed>
			<speedD random="0.2"/>
			<twoHitGap>0.2</twoHitGap>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<bounceD body="5" hurtNumAdd="0.8" />	<!-- 反弹 -->
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30" con="add">specialGun/rifleHornetBullet</bulletImgUrl>
			<smokeImgUrl raNum="30" con="filter">specialGun/rifleHornetBulletSmoke</smokeImgUrl>
			<hitImgUrl>bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet cnName="青蜂-跟踪飞镰">
			<name>sickle_godArmsSkill2</name>
			<cnName>青蜂-跟踪飞镰</cnName><armsType>rifle</armsType>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>8</shakeAngle>
			<bulletLife>4</bulletLife>
			<bulletWidth>20</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<extendGap>50</extendGap>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletAngle>0</bulletAngle>
			<bulletSpeed>25</bulletSpeed>
			<speedD random="0.2"/>
			<twoHitGap>0.2</twoHitGap>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<bounceD body="5" hurtNumAdd="0.8" />	<!-- 反弹 -->
			<followD value="1" noLM="1" />
			<skillArr>sickle_godArmsSkill2_link</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30" con="add">specialGun/rifleHornetBullet</bulletImgUrl>
			<smokeImgUrl raNum="30" con="filter">specialGun/rifleHornetBulletSmoke</smokeImgUrl>
			<hitImgUrl>bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
		<bullet cnName="赤鼬导弹发射器">
			<name>RifleHornetShooter</name>
			<cnName>赤鼬导弹发射器</cnName><armsType>shotgun</armsType>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletAngle>180</bulletAngle>
			<bulletLife>3</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.5</attackGap>
			<attackDelay>0.033</attackDelay>
			<bulletNum>1</bulletNum>				
			<shootNum>3</shootNum>
			<shootGap>0.066</shootGap>
			<shootPoint>-43,-4</shootPoint>
			
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<followD value="1" delay="0.3" />
			<boomD  bodyB="1" radius="250" noExcludeBodyB="1" maxHurtNum="5" hurtMul="3.7" />
			<skillArr>RifleHornetShooterHurt</skillArr>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>15</bulletSpeed>
			<speedD max="30" min="1" a="4" />
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">RifleHornetShooter/bullet1</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/smallBoom"  soundVolume="0.3"  shake="3,0.4,13">boomEffect/boom1</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="add" >RifleHornetShooter/smoke1</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		
		
		
		<bullet cnName="火首-武器技能">
			<name>flyDragonHead</name>
			<cnName>火首</cnName><armsType>flamer</armsType>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0.40</hurtRatio>
			<twoHitGap>0.5</twoHitGap>
			<twoHitSameNameB>1</twoHitSameNameB>
			<!--基本属性------------------------------------------------------------ -->.
			<bulletLife>3</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<bulletNum>1</bulletNum>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>30</bulletSpeed>
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>999</penetrationNum>
			<followD value="1" delay="0.1" />
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">specialGun/FlyDragonGunBullet</bulletImgUrl>
			<bulletLeftImgUrl raNum="30">specialGun/FlyDragonGunBullet2</bulletLeftImgUrl>
			<smokeImgUrl con="filter" raNum="30">specialGun/FlyDragonGunSmoke</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
			<hitImgUrl soundUrl="sound/magicHit2">bulletHitEffect/energyPurple</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
		<bullet cnName="炮仗-武器技能">
			<name>bangerGunSkill</name>
			<!--伤害属性------------------------------------------------------------ -->
			<cnName>炮仗</cnName><armsType>rifle</armsType>
			<hurtRatio>0.40</hurtRatio>
			<twoHitGap>0.05</twoHitGap>
			<!--基本属性------------------------------------------------------------ -->.
			<bulletLife>15</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<bulletNum>1</bulletNum>
			<bulletSkillArr>twoHit</bulletSkillArr>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>3</bulletSpeed>
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>999</penetrationNum>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30" con="add">bullet/fireball</bulletImgUrl>
			<hitImgUrl soundUrl="sound/magicHit2">boomEffect/boom2</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
		<bullet cnName="上帝之眼">
			<name>godEyes</name>
			<cnName>上帝之眼</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0.3</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>3</bulletLife>
			<bulletWidth>25</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>0</bulletAngle>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>					
								
			<shootAngle>0</shootAngle>					
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationNum>1000</penetrationNum>
			<bounceD floor="10" body="0" num="0"/>
			<skillArr>godEyesDefence</skillArr>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,0</shootPoint>
			<bulletSpeed>20</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">bullet/laser</bulletImgUrl>
			<hitImgUrl name="pistol1_hit"></hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter" raNum="30">bullet/laser</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		
		<bullet cnName="上帝之杖">
			<name>godMace</name>
			<cnName>上帝之杖</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>0.21</bulletLife>
			<bulletWidth>50</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletAngle>90</bulletAngle>
			<bulletNum>1</bulletNum>				
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,0</shootPoint>
			<bulletSpeed>60</bulletSpeed>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>1000</penetrationNum>
			<skillArr>godMaceDefence</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl></bulletImgUrl>
			<hitImgUrl soundUrl="sound/magicHit2">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
		<bullet cnName="剑齿镖-副手">
			<name>saberDartsBullet</name>
			<cnName>剑齿镖-副手</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>-179</bulletAngle>
			<bulletLife>30</bulletLife>
			<bulletWidth>50</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.7</attackGap>
			<attackDelay>0.33</attackDelay>
			<shootPoint>-68,-70</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>999</penetrationNum>
			<bulletSkillArr>saberDartsBullet</bulletSkillArr>
			<followD value="6" delay="999999" />
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>25</bulletSpeed>
			<speedD max="25" min="2"/>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="add">weapon/saberDartsEffect</bulletImgUrl>
			<hitImgUrl soundUrl="sound/body_hit">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
			
		</bullet>
		<bullet cnName="爆竹">
			<name>squibDevice</name>
			<cnName>爆竹</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0.1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>90</bulletAngle>
			<bulletLife>15</bulletLife>
			<bulletWidth>100</bulletWidth>
			<hitType>rect</hitType>
			<!--特殊属性------------------------------------------------------------ -->	
			<gravity>1</gravity>
			<boomD  selfB="1" radius="160"/>
			<bounceD glueFloorB="1" />
			<skillArr>squibDevice_screaming</skillArr>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>1</bulletSpeed>
			<speedD max="25" min="0"/>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl>bullet/squib</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom1">boomEffect/boom2</hitImgUrl><!-- 子弹图像【必备】 --><!-- 子弹图像【必备】 -->
			<hitFloorImgUrl soundUrl="sound/hitFloor">bulletHitEffect/yellow_motion</hitFloorImgUrl>
			<smokeImgUrl con="add" raNum="1">bullet/squibSpark</smokeImgUrl>
		</bullet>
		<bullet cnName="恐怖盒子">
			<name>terroristBox</name>
			<cnName>恐怖盒子</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>90</bulletAngle>
			<bulletLife>15</bulletLife>
			<bulletWidth>20</bulletWidth>
			<hitType>rect</hitType>
			<!--特殊属性------------------------------------------------------------ -->	
			<gravity>1</gravity>
			<boomD  selfB="1" radius="30" bodyB="1" />
			<bounceD glueFloorB="1" />
			<skillArr>terroristBox_screaming</skillArr>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>1</bulletSpeed>
			<speedD max="25" min="0"/>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl>terroristBoxEffect/box</bulletImgUrl>
			<selfBoomImgUrl soundUrl="terroristBoxEffect/hit1">terroristBoxEffect/effect</selfBoomImgUrl><!-- 子弹图像【必备】 --><!-- 子弹图像【必备】 -->
			<hitFloorImgUrl soundUrl="sound/hitFloor">bulletHitEffect/yellow_motion</hitFloorImgUrl>
		</bullet>
		
		<bullet cnName="装甲压制-飞镖">
			<name>zoomOutBullet</name>
			<cnName>放大-飞镖</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<noHitB>1</noHitB>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>-110</bulletAngle>
			<bulletLife>1</bulletLife>
			<hitType>rect</hitType>
			<shootNum>3</shootNum>
			<shootGap>0.3</shootGap>
			<!--特殊属性------------------------------------------------------------ -->	
			<boomD  selfB="1" radius="0"/>
			<bulletSkillArr>zoomOutBullet</bulletSkillArr>
			<followD value="1"/>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>20</bulletSpeed>
			<shootPoint>0,-80</shootPoint>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl>bullet/smallGaia</bulletImgUrl>
			<hitImgUrl soundUrl="sound/changeToZombie_enemy" con="add">boomEffect/showLight</hitImgUrl><!-- 子弹图像【必备】 --><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="filter">bulletHitEffect/spark_motion</smokeImgUrl>
		</bullet>
		<bullet cnName="装甲压制-盖亚">
			<name>zoomOutGaia</name>
			<cnName>装甲压制-盖亚</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>1</hurtRatio>
			<noHitB>1</noHitB>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>90</bulletAngle>
			<bulletLife>10</bulletLife>
			<hitType>rect</hitType>
			<!--特殊属性------------------------------------------------------------ -->	
			<gravity>3</gravity>
			<boomD  selfB="1" radius="150"/>
			<bulletSkillArr>zoomOutGaia</bulletSkillArr>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>10</bulletSpeed>
			<speedD max="40" min="10" a="10" />
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl>bullet/bigGaia</bulletImgUrl>
			<hitImgUrl soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</hitImgUrl><!-- 子弹图像【必备】 -->
			
		</bullet>
	</father>
	
	<father type="arms" cnName="武器">
		<bullet cnName="熔炉-子弹">
			<name>rocketMammothSun</name>
			<cnName>熔炉-子弹</cnName><armsType>rocket</armsType>
			<noMagneticB>1</noMagneticB>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0.9</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>0</bulletAngle>
			<bulletLife>1</bulletLife>
			<lifeRandom>0.2</lifeRandom>
			<bulletWidth>25</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletNum>7</bulletNum>
			<shootAngle>20</shootAngle>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>20</bulletSpeed>
			<speedD a="-2" min="2" random="0.2" />
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">bullet/gaiaSmallBullet</bulletImgUrl>
			<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom2" soundVolume="0.3">boomEffect/boom1</hitImgUrl>
		</bullet>
		
		
		<bullet cnName="烟花-子弹">
			<name>fireworksSun</name>
			<cnName>烟花-子弹</cnName><armsType>crossbow</armsType>
			<noMagneticB>1</noMagneticB>
			<noHitTime>0.4</noHitTime>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>0.2</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletNum>1</bulletNum>
			<bulletAngle>0</bulletAngle>
			<bulletLife>2.5</bulletLife>
			<lifeRandom>0.1</lifeRandom>
			<bulletWidth>25</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<extendGap>10</extendGap>
			<shootAngle>-90</shootAngle>
			<shootShakeAngle>360</shootShakeAngle>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>10</bulletSpeed>
			<speedD random="0.2" />
			<gravity>0.5</gravity>
			<penetrationGap>1000</penetrationGap>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="filter" raNum="30" >specialGun/electricLine</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom2" soundVolume="0.3">bulletHitEffect/energy</hitImgUrl>
		</bullet>
		
	</father>
	<father type="task" cnName="任务">
		<bullet cnName="枪林弹雨-导弹">
			<name>bulletRainBullet</name><noMagneticB>1</noMagneticB><noBeClearB>1</noBeClearB>
			<cnName>枪林弹雨-导弹</cnName>
			<hurtRatio>0</hurtRatio>
			<hurtMul>2</hurtMul>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>20</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletAngle>180</bulletAngle>
			<bulletSpeed>8</bulletSpeed>
			<speedD random="0.4" />
			<penetrationGap>1000</penetrationGap>
			<boomD selfB="1"/>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl>bullet/gaiaBulletFire</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom2">boomEffect/boom2</hitImgUrl>
			<selfBoomImgUrl soundVolume="0.2" soundUrl="boomSound/midBoom2">boomEffect/boom2</selfBoomImgUrl>
		</bullet>
		<bullet cnName="枪林弹雨-闪电球">
			<name>bulletRainBall</name><noMagneticB>1</noMagneticB><noBeClearB>1</noBeClearB>
			<cnName>枪林弹雨-闪电球</cnName>
			<hurtRatio>1</hurtRatio>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>15</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletAngle>180</bulletAngle>
			<bulletSpeed>5</bulletSpeed>
			<penetrationGap>1000</penetrationGap>
			<skillArr>bulletRainBallHit</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl con="add">skillEffect/paralysis_enemy_bullet</bulletImgUrl>
		</bullet>
		
		<bullet cnName="荆棘之路">
			<name>thornyRoad</name><sameCampB>0</sameCampB><noMagneticB>1</noMagneticB><noBeClearB>1</noBeClearB>
			<cnName>荆棘之路</cnName>
			<hurtMul>0.1</hurtMul>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>9999999</bulletLife>
			<bulletWidth>20</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletAngle>0</bulletAngle>
			<bulletSpeed>0</bulletSpeed>
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>999</penetrationNum>
			<twoHitGap>1</twoHitGap>
			<skillArr>killAll,invincibleEmp,enemyEmp</skillArr>
			<flipX>1</flipX>
			<bulletImgUrl>bullet/thorns</bulletImgUrl>
			<hitImgUrl con="add" soundUrl="sound/vehicle_hit1">bulletHitEffect/energy</hitImgUrl>
		</bullet>
		
		<bullet cnName="保卫北斗">
			<name>defendBeiDou</name><sameCampB>0</sameCampB><noMagneticB>1</noMagneticB><noBeClearB>1</noBeClearB>
			<cnName>保卫北斗</cnName>
			<hurtMul>1</hurtMul>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>9999999</bulletLife>
			<bulletWidth>20</bulletWidth>
			<hitType>rect</hitType>
			
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletAngle>90</bulletAngle>
			<bulletSpeed>7</bulletSpeed>
			<speedD random="0.3" />
			<boomD selfB="1" hurtMul="0"/>
			<bindingD cnName="空单位" lifeMul="0.00001"/>
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>999</penetrationNum>
			<skillArr>killAll</skillArr>
			<flipX>1</flipX>
			<bulletImgUrl con="filter" raNum="1">generalEffect/fire</bulletImgUrl>
			<selfBoomImgUrl soundUrl="sound/fireHit2">boomEffect/boom3</selfBoomImgUrl>
			<hitFloorImgUrl soundUrl="boomSound/bigBoom">boomEffect/bigCircle</hitFloorImgUrl>
		</bullet>
	</father>
</data>