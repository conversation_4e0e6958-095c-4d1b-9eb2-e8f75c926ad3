<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="space">
		<body>
			<name>Or<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></name>
			<cnName>葵型采矿机</cnName>
			<raceType>ship</raceType>
			<swfUrl>swf/ship/OreFlowerBoss.swf</swfUrl>
			<!-- 图像 -->
			<dieImg name="bigSpace"/><imgType>normal</imgType>
			<dieJumpMul>0</dieJumpMul><lifeBarExtraHeight>-80</lifeBarExtraHeight>
			<imgArr>
				stand,laserAttack,sprintAttack,die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-20,-20,40,40</hitRect>
			<hurtRectArr>-66,-90,120,180</hurtRectArr>
			<motionState>fly</motionState>
			<flyType>space</flyType>
			<motionD vRan="0" F_AIR="4" dieEN="5" />
			<maxVx>6</maxVx>
			<attackAIClass></attackAIClass><!--SimpleSpaceAttack_AI  -->
			<bossSkillArr>OreFlowerBoss</bossSkillArr>
			<hurtArr>
				<hurt>
					<imgLabel>sprintAttack</imgLabel>
					<hurtRatio>0.05</hurtRatio>
					<grapRect>-520,-110,530,210</grapRect>
					<shakeValue>10</shakeValue>
					<hitImgUrl name="hurtBulletMad_hit"/>
				</hurt>
				<hurt cd="10">
					<imgLabel>laserAttack</imgLabel><ingfollowB>1</ingfollowB>
					<hurtRatio>0.2</hurtRatio>
					<shakeValue>10</shakeValue>
					<hitImgUrl name="GasDefense_1_hit"/>
				</hurt>
				
			</hurtArr>
		</body>
		
				<skill>
					<name>OreFlowerBoss</name><ignoreSilenceB>1</ignoreSilenceB><ignoreNoSkillB>1</ignoreNoSkillB>
					<cnName>采矿掘进机buff</cnName><everNoClearB>1</everNoClearB><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
					<conditionType>passive</conditionType>
					<condition>add</condition>
					<target>me</target>
					<!--效果------------------------------------------------------------ -->
					<addType>state</addType><summonedUnitsB>1</summonedUnitsB>
					<effectType>OreFlowerBoss</effectType><effectFather>oreSpace</effectFather>
					<obj>"cnName":"采矿球","num":1,"lifeMul":1,"dpsMul":1,"cx":126,"cy":19,"lifeTime":-1</obj>
					<valueString>stand</valueString>
					<pointEffectImg name="oreBombShowFlower"/>
					<duration>9999999</duration>
				</skill>
				
		
		
		
		
		<body>
			<name>OreFlower</name>
			<cnName>采矿花</cnName><lifeRatio>6</lifeRatio>
			<raceType>ship</raceType>
			<swfUrl>swf/ship/OreFlower.swf</swfUrl>
			<!-- 图像 -->
			<dieImg name="midSpace"/>
			<dieJumpMul>0</dieJumpMul>
			
			<imgArr>
				stand,die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-20,-20,40,40</hitRect>
			<hurtRectArr>-37,-42,74,84</hurtRectArr>
			<motionState>fly</motionState>
			<flyType>space</flyType>
			<motionD vRan="0" F_AIR="4"/>
			<maxVx>5</maxVx>
			<attackAIClass></attackAIClass><!--SimpleSpaceAttack_AI  -->
			<skillArr>OreFlowerAI,hitCheckDie</skillArr>
			<bossSkillArr>groupSpeedUp_enemy</bossSkillArr>
			
		</body>
				<skill>
					<name>OreFlowerAI</name><ignoreSilenceB>1</ignoreSilenceB><ignoreNoSkillB>1</ignoreNoSkillB>
					<cnName>采矿花buff</cnName><everNoClearB>1</everNoClearB><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
					<conditionType>passive</conditionType>
					<condition>add</condition>
					<target>me</target>
					<!--效果------------------------------------------------------------ -->
					<addType>state</addType><summonedUnitsB>1</summonedUnitsB>
					<effectType>OreFlowerAI</effectType><effectFather>oreSpace</effectFather>
					<obj>"cnName":"矿锯","num":1,"lifeMul":1,"dpsMul":1,"cx":55,"cy":0,"lifeTime":-1</obj>
					<valueString>stand</valueString>
					<pointEffectImg name="oreBombShowFlower"/>
					<duration>9999999</duration>
				</skill>

		
		
				
		<body>
			<name>OreBomb</name>
			<cnName>矿锯</cnName><lifeRatio>0.5</lifeRatio>
			<raceType>ship</raceType>
			<swfUrl>swf/ship/OreBomb.swf</swfUrl>
			<!-- 图像 -->
			<dieImg name="stoneBoom"/>
			<dieJumpMul>0</dieJumpMul>
			<lockLeftB>1</lockLeftB>
			<imgArr>
				stand,die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-25,-25,50,50</hitRect>
			<motionState>fly</motionState>
			<flyType>space</flyType>
			<motionD vRan="0" F_AIR="2"/>
			<maxVx>11</maxVx>
			<attackAIClass></attackAIClass><!--SimpleSpaceAttack_AI  -->
			<skillArr>OreBombBuff,hitCheckDie6</skillArr>
		</body>
				<skill>
					<name>OreBombBuff</name><ignoreSilenceB>1</ignoreSilenceB><ignoreNoSkillB>1</ignoreNoSkillB>
					<cnName>矿锯buff</cnName><everNoClearB>1</everNoClearB><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
					<conditionType>passive</conditionType>
					<condition>add</condition>
					<target>me</target>
					<!--效果------------------------------------------------------------ -->
					<addType>state</addType>
					<effectType>OreBomb</effectType>
					<mul>1</mul>
					<duration>6</duration><!-- 最多存活6秒 -->
					<stateEffectImg name="oreBombState"/>
				</skill>
	</father>
</data>