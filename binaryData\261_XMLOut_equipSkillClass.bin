<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="headSkill" cnName="头盔、腰带--装备技能-不能使用condition=add的参数、不能使用addSkillEffectImg参数，否则会出错">
		<!-- 头盔、腰带技能 -->
		<skill index="0" cnName="上帝的护佑"><!-- dps -->
			<name>godHand_equip</name>
			<cnName>上帝的护佑</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>beforeDie</condition><!-- 被攻击后触发 -->
			<minTriggerT>55</minTriggerT>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>godHand</effectType>
			<value>1</value>
			<duration>3</duration>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/invisibility_hero" con="add"></meEffectImg>
			<description>在受到必亡的伤害时，你将接受上帝的护佑，进入隐身无敌模式，持续[duration]秒，同时剩余1点生命值。技能触发间隔不小于50秒。</description>
		</skill>
		<skill index="0" cnName="免疫"><!-- dps -->
			<name>immune_equip</name>
			<cnName>免疫</cnName><showInLifeBarB>1</showInLifeBarB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underEnemySkill</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>immune</effectType>
			<effectProArr>0.4</effectProArr>
			<mul>1</mul>
			<targetEffectImg con="add" partType="body">skillEffect/immune_equip</targetEffectImg>
			<description>当敌人对你释放技能时，有[effectProArr.0]几率使该技能对你无效。</description>
		</skill>
		<skill index="0" cnName="磁力场"><!-- 生存-主动 -->
			<name>magneticField_equip</name>
			<cnName>磁力场</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<intervalT>25</intervalT>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>magneticB</effectType>
			<value>1</value>
			<mul>0.2</mul>
			<duration>7</duration>
			<range>200</range>
			<!--技能链接或者需要添加的被动技能------------------------------------------------------------ --> 
			<passiveSkillArr>magneticField_enemy_link</passiveSkillArr>
			<!--图像------------------------------------------------------------ --> 
			<meEffectImg soundUrl="sound/magneticField"></meEffectImg>
			<stateEffectImg  partType="mouth" con="add">skillEffect/magneticField</stateEffectImg>
			<description>每过[intervalT]秒向外释放磁力场，使700码的范围内的所有敌方子弹偏离轨道，持续[duration]秒。</description>
		</skill>
		<skill index="0" cnName="顽强光环"><!-- dps -->
			<name>strongHalo_equip</name>
			<cnName>顽强光环</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target>me,range,we</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>underHurtMul</effectType>
			<mul>0.85</mul>
			<duration>2</duration>
			<range>800</range>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="mouth" con="add">skillEffect/rune_red_shield</stateEffectImg>
			<description>为周围[range]码以内的我方单位增加[1-mul]的防御力。</description>
		</skill>
		<skill index="0" cnName="嗜爪之怒"><!-- dps -->
			<name>murderous_equip</name>
			<cnName>嗜爪之怒</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHit</condition>
			<minTriggerT>10</minTriggerT>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>murderous_addHurtMul</effectType>
			<effectProArr>0.10</effectProArr>
			<mul>2.5</mul>
			<duration>6</duration>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/murderous_hero"></meEffectImg>
			<stateEffectImg partType="2hand" con="add" raNum="15" followPartRaB="1">skillEffect/murderous_enemy</stateEffectImg>
			<description>每次受到攻击都有[effectProArr.0]的几率进入嗜爪状态，增加[mul-1]的攻击力，持续[duration]秒。技能触发间隔不小于[minTriggerT]秒。</description>
		</skill>
		<skill index="0" cnName="瘴气">
			<name>poisonRange_equip</name>
			<cnName>瘴气</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>useSkill</condition><!-- 条件	underHit：受到攻击，die：死亡，hit：攻击目标，killTarget：消灭目标，interval：每隔duration秒 -->
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>poison</effectType>
			<extraValueType>nowArmsDpsOrNormal</extraValueType>
			<value>0</value>
			<mul>0.5</mul>
			<doGap>1</doGap>
			<duration>5</duration>
			<range>450</range>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/poisonousFog_hero"></meEffectImg>
			<stateEffectImg partType="mouth" con="add">skillEffect/poisonousFog_hero</stateEffectImg>
			<description>每次释放任何主动技能，都会使周围[range]码内的敌人中毒，每秒受到伤害，伤害值为施法者当前武器战斗力的[mul]，持续[duration]秒。</description>
		</skill>
		<skill index="0" cnName="耐久光环"><!-- dps -->
			<name>attackSpeedHalo_equip</name>
			<cnName>耐久光环</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target>me,range,we</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>attackGapMul</effectType>
			<mul>1.13</mul>
			<duration>2</duration>
			<range>700</range>
			<!--图像------------------------------------------------------------ -->
			<description>为周围[range]码以内的我方射击单位增加[mul-1]的攻击速度。</description>
		</skill>
	</father>	
	<father name="coatSkill" cnName="战衣、战裤---装备技能-不能使用condition=add的参数、不能使用addSkillEffectImg参数，否则会出错">
		<!-- 战衣、战裤技能 -->
		<skill index="0" cnName="牺牲"><!-- dps -->
			<name>sacrifice_equip</name>
			<cnName>牺牲</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHurt</condition>
			<target>me,range,we</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>sacrifice</effectType>
			<mul>0.15</mul>
			<range>450</range>
			<description>每次受到伤害都能增加[range]范围内我方单位的生命值，增加值为伤害值的[mul]。</description>
		</skill>
		<skill index="0" cnName="钢背"><!-- dps-被动 -->
			<name>backStrong_equip</name>
			<cnName>钢背</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurt</effectType>
			<mul>0.65</mul>
			<!-- 修改伤害所需 -->
			<obj>"type":"backWeak"</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<targetEffectImg></targetEffectImg>
			<description>减少来自背面[1-mul]的伤害。</description>
		</skill>
		<skill index="0" cnName="负离子外壳"><!-- 限制 -->
			<name>anionSkin_equip</name>
			<cnName>负离子外壳</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHit</condition>
			<minTriggerT>5</minTriggerT>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>anionSkin</effectType>
			<effectProArr>0.2</effectProArr>
			<mul>0.5</mul>
			<duration>3</duration>
			<!--图像------------------------------------------------------------ -->
			<targetEffectImg soundUrl="sound/paralysis_enemy_hit"></targetEffectImg>
			<stateEffectImg con="add">skillEffect/paralysis_enemy</stateEffectImg>
			<description>自身被攻击时，攻击者有[effectProArr.0]的几率被麻痹，无法移动，攻击速度降低[1-mul]，持续[duration]秒。</description>
		</skill>
		<skill index="0" cnName="净化器"><!-- 生存-主动 -->
			<name>treater_equip</name>
			<cnName>净化器</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<intervalT>6</intervalT>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>clearEnemyState</effectType>
			<!--图像------------------------------------------------------------ --> 
			<targetEffectImg con="add">skillEffect/treater_equip</targetEffectImg>
			<description>每隔[intervalT]秒清除1次自身负面状态。</description>
		</skill>
		<skill index="0" cnName="芒刺"><!-- dps-被动 -->
			<name>backWeak_equip</name>
			<cnName>芒刺</cnName><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurt</effectType>
			<mul>1.35</mul>
			<!-- 修改伤害所需 -->
			<obj>"type":"backWeak"</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<targetEffectImg></targetEffectImg>
			<description>攻击敌人背部或者后脑勺都会造成额外[mul-1]的伤害。</description>
		</skill>
		<skill index="0" cnName="荆棘外表"><!-- 限制 -->
			<name>thornSkin_equip</name>
			<cnName>荆棘外表</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHit</condition>
			<minTriggerT>5</minTriggerT>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>dizziness</effectType>
			<effectProArr>0.2</effectProArr>
			<duration>2</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="mouth" con="add">skillEffect/dizziness</stateEffectImg>
			<description>自身被攻击时，攻击者有[effectProArr.0]的几率进入眩晕状态，持续[duration]秒。</description>
		</skill>
		<skill index="0" cnName="折射"><!-- 限制 -->
			<name>refraction_equip</name>
			<cnName>折射</cnName>
			<noEffectLevelModel>arena</noEffectLevelModel>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>refraction</effectType>
			<effectProArr>0.15</effectProArr>
			<range>350</range>
			<!--图像------------------------------------------------------------ -->
			<description>自身被攻击时，有[effectProArr.0]的几率不受伤害，同时使周围[range]内的敌人受到同等伤害。</description>
		</skill>
	</father>		
	<father name="fashionSkill">
		<skill cnName="召唤群狼"><!-- dps -->
			<name>summonWolf_bigBoss</name>
			<cnName>召唤群狼</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>usebutcherBlade</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<extraValueType>nowArmsTrueDps</extraValueType>
			<mul>1</mul>
			<!-- 子弹所需 -->
			<obj>"name":"summonWolf_bigBoss","site":"me","flipB":true,"launcherB":true</obj>
			<!--图像------------------------------------------------------------ -->
			<description>每次挥舞屠夫都将召唤成群的尸狼灵魂向前狂奔，对被冲撞的敌人造成伤害。</description>
		</skill>
		<skill cnName="装甲压制"><!-- dps -->
			<name>zoomOut</name>
			<cnName>装甲压制</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<intervalT>5</intervalT>
			<target noExistB="1">me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet_zoomOut</effectType>
			<extraValueType>nowArmsTrueDps</extraValueType>
			<mul>3</mul>
			<range>99999</range>
			<!-- 子弹所需 -->
			<obj>"name":"zoomOutBullet","nameArr":["zoomOutGaia"],"site":"me","flipB":true,"launcherB":1</obj>
			<!--图像------------------------------------------------------------ -->
			<description>在骑乘赤焰、幽鬼等街跑载具时，每隔[intervalT]秒，向敌方丢出3辆变大的玩具坦克，产生300%当前武器战斗力的爆炸伤害。</description>
		</skill>
	</father>
	
	<father name="equipSkill_link" cnName="装备技能-链接">
		<skill index="0" name="magneticField_enemy_link" cnName="磁力场-曲扭光环"><!-- 生存-主动 -->
			<name>magneticField_enemy_link</name>
			<cnName>磁力场-曲扭光环</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>interval</condition>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>magneticField</effectType>
			<range>800</range>
			<duration>0.5</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg partType="shootPoint" con="add" raNum="30" followPartRaB="1">skillEffect/magneticField_paralysis</stateEffectImg>
			<description>使目标射出的子弹偏离轨道，持续[duration]秒。</description>
		</skill>
		<skill index="0" cnName="折射-不受伤害"><!-- 限制 -->
			<name>refraction_equip_link</name>
			<cnName>折射</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>noUnderHurtB</effectType>
		</skill>
	</father>	
	<father name="fashionSkill" cnName="时装技能">
		
	</father>
</data> 