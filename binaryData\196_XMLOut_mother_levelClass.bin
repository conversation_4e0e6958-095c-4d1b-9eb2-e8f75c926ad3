<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="normal">
		<gather cnName="撒旦的测试">
			<level name="arcHowMain1">
				<sceneLabel>XiaSha</sceneLabel>
				<!-- 关卡数据 -->
				<info enemyLv="99" diy="wotuBack" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<drop noB="1"/>
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 人类 -->
					<unitOrder id="we1"  camp="we">
						<unit cnName="本我" armsRange="arcHow" lifeMul="1" dpsMul="5000" skillArr="jumpNumAddZero" />
						<unit cnName="雇佣兵" dieGotoState="stru" aiOrder="followBody:本我" skillArr="State_noAiFind,State_InvincibleThrough" />
					</unitOrder>
					<unitOrder id="enemy1">
						<unit cnName="毒蛛" armsRange="arcHow" lifeMul="1" dpsMul="500" skillArr="BallLightningEffect,noAttackOrder,noUnderFlyHit,State_SpellImmunity,bladeShield,likeMissleNo" noSuperB="1" />
						<![CDATA[midLightning,BallLightningEffect,noAttackOrder,noUnderFlyHit,State_SpellImmunity,bladeShield,likeMissleNo]]>
					</unitOrder>
				</unitG>
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">192,60,232,120</rect>
					<rect id="r_over">2956,1073,53,113</rect>
					<rect id="r1">2944,1067,56,127</rect>
					<rect id="r2">1034,-16,300,134</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">45,111,80,46</rect>
					<rect label="addCharger">2400,838,80,46</rect>
				</rectG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order>
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:本我</order>
							<order>level;taskTimingB:false</order>
						</event>
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>level;taskTimingB:true</order>
						</event>
						<![CDATA[产生敌人，循环30次]]>
						<event>
							<condition doNumber="15">liveEnemyNumber:less_1</condition>
							<order>createUnit:enemy1; r_arcHowMain</order>
						</event>
						<event>
							<condition delay="0.5">liveEnemyNumber:less_1</condition>
							<order>level;taskTimingB:false</order>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; 本我</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="arcHowMain2">
				<sceneLabel>QingMing</sceneLabel>
				<!-- 关卡数据 -->
				<info preBulletArr="arcHowTaskFire" enemyLv="99" diy="arcHowMain" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<drop noB="1"/>
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 人类 -->
					<unitOrder id="we1"  camp="we">
						<unit cnName="本我" armsRange="arcHow" lifeMul="1" dpsMul="5000" skillArr="" />
						<unit cnName="雇佣兵" dieGotoState="stru" aiOrder="followBody:本我" skillArr="State_noAiFind,State_InvincibleThrough" />
					</unitOrder>
					<unitOrder id="enemy1">
						<unit cnName="毒蛛" armsRange="arcHow" lifeMul="1" dpsMul="500" skillArr="BallLightningEffect,noAttackOrder,noUnderFlyHit,State_SpellImmunity,bladeShield,likeMissleNo" noSuperB="1" />
					</unitOrder>
				</unitG>
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">1850,820,330,106</rect>
					<rect id="r_over">-30,800,80,135</rect>
					<rect id="r1">20,50,330,106</rect>
					<rect id="r2">2768,192,330,106</rect>
					<rect id="r3">1280,46,330,106</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">2072,310,84,48</rect>
					<rect label="addCharger">122,868,84,66</rect>
				</rectG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order>
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:本我</order>
							<order>level;taskTimingB:false</order>
						</event>
						
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>level;taskTimingB:true</order>
						</event>
						
						<![CDATA[产生敌人，循环30次]]>
						<event>
							<condition doNumber="15">liveEnemyNumber:less_1</condition>
							<order>createUnit:enemy1; r_heroRange1200</order>
						</event>
						<event>
							<condition delay="0.5">liveEnemyNumber:less_1</condition>
							<order>level;taskTimingB:false</order>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>clearAllEnemyBullet</order>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; 本我</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="arcHowMain3">
				<sceneLabel>QingSha</sceneLabel>
				<!-- 关卡数据 -->
				<info preBulletArr="arcHowTaskFire" enemyLv="99" diy="arcHowMain" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<drop noB="1"/>
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 人类 -->
					<unitOrder id="we1"  camp="we">
						<unit cnName="本我" armsRange="arcHow" lifeMul="1" dpsMul="5000" skillArr="" />
						<unit cnName="雇佣兵" dieGotoState="stru" aiOrder="followBody:本我" skillArr="State_noAiFind,State_InvincibleThrough" />
					</unitOrder>
					<unitOrder id="enemy1">
						<unit cnName="毒蛛" armsRange="arcHow" lifeMul="1" dpsMul="500" skillArr="BallLightningEffect,noAttackOrder,noUnderFlyHit,State_SpellImmunity,bladeShield,likeMissleNo" noSuperB="1" />
					</unitOrder>
				</unitG>
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">2241,638,238,85</rect>
					<rect id="r_over">3860,610,80,106</rect>
					<rect id="r1">30,582,388,112</rect>
					<rect id="r2">3600,582,388,112</rect>
					<rect id="r3">2137,-76,342,106</rect>
					
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">1294,652,84,66</rect>
					<rect label="addCharger">3348,484,84,66</rect>
				</rectG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order>
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:本我</order>
							<order>level;taskTimingB:false</order>
						</event>
						<![CDATA[
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>level;taskTimingB:true</order>
						</event>
						]]>
						<![CDATA[产生敌人，循环30次]]>
						<event>
							<condition doNumber="15">liveEnemyNumber:less_1</condition>
							<order>createUnit:enemy1; r_arcHowMainQingSha</order>
						</event>
						<event>
							<condition delay="0.5">liveEnemyNumber:less_1</condition>
							<order>level;taskTimingB:false</order>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>clearAllEnemyBullet</order>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; 本我</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="arcHowMain4">
				<sceneLabel>FengWei</sceneLabel>
				<!-- 关卡数据 -->
				<info preBulletArr="arcHowTaskFire" enemyLv="99" diy="arcHowMain" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<drop noB="1"/>
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 人类 -->
					<unitOrder id="we1"  camp="we">
						<unit cnName="本我" armsRange="arcHow" lifeMul="1" dpsMul="5000" skillArr="jumpNumAdd1" />
						<unit cnName="雇佣兵" dieGotoState="stru" aiOrder="followBody:本我" skillArr="State_noAiFind,State_InvincibleThrough" />
					</unitOrder>
					<unitOrder id="enemy1">
						<unit cnName="毒蛛" armsRange="arcHow" lifeMul="1" dpsMul="500" skillArr="verShield,midLightning,BallLightningEffect,noAttackOrder,noUnderFlyHit,State_SpellImmunity,bladeShield,likeMissleNo" noSuperB="1" />
					</unitOrder>
				</unitG>
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">1734,640,145,83</rect>
					<rect id="r_over">3376,1008,56,127</rect>
					<rect id="r1">20,968,442,158</rect>
					<rect id="r2">3000,968,442,158</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">514,900,145,83</rect>
					<rect label="addCharger">2600,900,145,83</rect>
				</rectG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order>
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:本我</order>
							<order>level;taskTimingB:false</order>
						</event>
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>level;taskTimingB:true</order>
						</event>
						<event>
							<condition doNumber="15">liveEnemyNumber:less_1</condition>
							<order>createUnit:enemy1; r_heroRange800</order>
						</event>
						<event>
							<condition delay="0.5">liveEnemyNumber:less_1</condition>
							<order>level;taskTimingB:false</order>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>clearAllEnemyBullet</order>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; 本我</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<![CDATA[
			<level name="arcHowMain4">
				<sceneLabel>BaoLun</sceneLabel>
				<!-- 关卡数据 -->
				<info preBulletArr="arcHowTaskFire" enemyLv="99" diy="arcHowMain" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<drop noB="1"/>
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 人类 -->
					<unitOrder id="we1"  camp="we">
						<unit cnName="本我" armsRange="arcHow" lifeMul="1" dpsMul="5000" skillArr="" />
						<unit cnName="雇佣兵" dieGotoState="stru" aiOrder="followBody:本我" skillArr="State_noAiFind,State_InvincibleThrough" />
					</unitOrder>
					<unitOrder id="enemy1">
						<unit cnName="毒蛛" armsRange="arcHow" lifeMul="1" dpsMul="500" skillArr="BallLightningEffect,noAttackOrder,noUnderFlyHit,State_SpellImmunity,bladeShield,likeMissleNo" noSuperB="1" />
					</unitOrder>
				</unitG>
				<!-- 区域集 -->
				<rectG>
					<rect id="r_birth">1429,571,188,62</rect>
					<rect id="r_over">2955,548,57,121</rect>
					<rect id="r_back">-300,548,57,121</rect>
					<rect id="r1">28,64,394,108</rect>
					<rect id="r2">2578,64,394,108</rect>
					<rect id="r3">1230,-40,394,108</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">2161,203,84,84</rect>
					<rect label="addCharger">931,203,84,84</rect>
				</rectG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order>
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:本我</order>
							<order>level;taskTimingB:false</order>
						</event>
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>level;taskTimingB:true</order>
						</event>
						<event>
							<condition doNumber="30">liveEnemyNumber:less_1</condition>
							<order>createUnit:enemy1; r_arcHowMainBaoLun</order>
						</event>
						<event>
							<condition delay="0.5">liveEnemyNumber:less_1</condition>
							<order>level;taskTimingB:false</order>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; 本我</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			]]>
		</gather>
		<gather cnName="狂人的烦恼">
			<level name="madmanTroubles">
				<sceneLabel>MainUpland</sceneLabel>
				<!-- 关卡数据 -->
				<info enemyLv="99" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<drop noB="1"/>
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 人类 -->
					<unitOrder id="weWenJie"  camp="we">
						<unit cnName="文杰表哥"/>
					</unitOrder>
					<unitOrder id="weYing"  camp="we">
						<unit cnName="小樱" aiOrder="followBody:文杰表哥"/>
					</unitOrder>
					<unitOrder id="weMad"  camp="we">
						<unit cnName="战争狂人"/>
					</unitOrder>
					<unitOrder id="weShip"  camp="we">
						<unit id="氩星舰" cnName="氩星舰"/>
					</unitOrder>
				</unitG>
				<eventG>
					
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order>
							<order>createUnit:weWenJie; r_birth</order>
							<order>heroNoExistXY:1080,600</order>
						</event>
						<event>
							<condition delay="0.5"></condition>
							<order>say; startList:sayMother</order>
							<order>body:文杰表哥;followPoint:1080,600</order>
							<order>body:文杰表哥; setShootXY:-190,512</order>
						</event>
						<![CDATA[对话结束，小樱走出来]]>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>createUnit:weYing; r_over</order>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:sayYing</order>
						</event>
						<![CDATA[对话结束，表哥和小樱往左边跑]]>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>body:文杰表哥; rangeLimit:close</order>
							<order>body:文杰表哥; lockShootXY:-190,512</order>
							<order>body:小樱; rangeLimit:close</order>
							<order>body:小樱; lockShootXY:-190,512</order>
							
							<order>body:文杰表哥;followPoint:-600,600</order>
						</event>
						<![CDATA[狂人出来]]>
						<event>
							<condition delay="3"></condition>
							<order>createUnit:weMad; r_over</order>
							<order>body:战争狂人;StrikerHead</order>
							<order>body:战争狂人;followPoint:1080,600</order>
							<order>body:战争狂人; setShootXY:-190,512</order>
						</event>
						<event>
							<condition delay="3"></condition>
							<order>heroEverParasitic:战争狂人</order>
							<order>say; startList:sayMad</order>
						</event>
						<![CDATA[对话结束，飞船出现，播放音效]]>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>createUnit:weShip; 1080,-200</order>
							<order>body:氩星舰;blendMode:add</order>
							<order>body:氩星舰;followPoint:1080,420</order>
							<order>playSound:ArgonShip/goIn</order>
							
						</event>
						<event>
							<condition delay="2"></condition>
							<order>say; startList:sayShip</order>
							<order>body:氩星舰;playBreak:waveAttack</order>
						</event>
						<![CDATA[对话结束，飞船飞走]]>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>body:氩星舰;followPoint:400,-400</order>
							<order>playSound:ArgonShip/goOut</order>
						</event>
						<![CDATA[铁犬来电]]>
						<event>
							<condition delay="1.5"></condition>
							<order>say; startList:sayDog</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
		<gather cnName="叛徒、铁犬的追捕、击退铁犬">
			<level name="mochaExcape">
				<!-- 关卡数据 -->
				<info enemyLv="99" diy="wotuBack" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<drop noB="1"/>
				<!-- 基本属性 -->
				<sceneLabel>QingMing</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					<!-- 我方 -->
					<unitOrder id="we1" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="摩卡" dpsMul="500" lifeMul="0.05" armsRange="sniperCicada$2,shotgunSkunkTwo$2" skillArr="crazy_enemy,groupLight_hero_7,hiding_hero_3,jumpNumAdd1,State_AddMove50" />
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="僵尸突击兵" num="1" lifeMul="3" dpsMul="1.5" skillArr="redEyes" armsRange="christmasGun" />
						<unit cnName="僵尸空降兵" num="1" lifeMul="3" skillArr="redEyes" armsRange="bangerGun" />
						<unit cnName="僵尸暴枪兵" num="1" lifeMul="3" skillArr="redEyes,murderous_hero" armsRange="penGun" />
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="防暴僵尸" num="1" lifeMul="3" skillArr="redEyes" armsRange="yearDog" />
						<unit cnName="僵尸狙击兵" num="1" lifeMul="6" skillArr="redEyes" armsRange="rocketMammoth" />
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="鬼目射手" num="1" lifeMul="6" dpsMul="2" skillArr="redEyes" armsRange="beadCrossbow" />
						<unit cnName="僵尸炮兵总管" num="1" lifeMul="3" skillArr="redEyes" armsRange="redFire" />
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="鬼影战士" num="1" lifeMul="15" dpsMul="0.3"  skillArr="redEyes" armsRange="rocketCate"/>
					</unitOrder>
					<unitOrder id="enemy5">
						<unit cnName="火炮僵尸王" num="1" lifeMul="9" dpsMul="0.4" skillArr="redEyes,fastForward_enemy" skillCloseB="1" armsRange="yearDragon"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1850,820,330,106</rect>
					<rect id="r_over">-30,800,80,135</rect>
					<rect id="r1">20,50,330,106</rect>
					<rect id="r2">2768,192,330,106</rect>
					<rect id="r3">1280,46,330,106</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">2072,310,84,48</rect>
					<rect label="addCharger">122,868,84,66</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order>
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:摩卡</order>
						</event>
						<![CDATA[]]>
						<event id="e1_1">
							<condition doNumber="3" orderChooseType="randomOne">liveEnemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_2">
							<condition doNumber="2" orderChooseType="randomOne">liveEnemyNumber:less_2</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_3">
							<condition doNumber="2" orderChooseType="randomOne">liveEnemyNumber:less_2</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e1_4">
							<condition doNumber="1" orderChooseType="randomOne">liveEnemyNumber:less_2</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
							<order>createUnit:enemy4; r3</order>
						</event>
						<event id="e1_5">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy5; r1</order>
							<order>createUnit:enemy5; r2</order>
							<order>createUnit:enemy5; r3</order>
						</event>
						<event id="e1_5">
							<condition>enemyNumber:less_1</condition>
							<order>say; startList:s1</order>
						</event>
						<event id="e1_5">
							<condition delay="1">say:listOver</condition>
							<order>say; startList:s2</order>
						</event>
						<event id="e_9">
							<condition>say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; 摩卡</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="mochaIronDog">
				<!-- 关卡数据 -->
				<info enemyLv="99" diy="wotuBack" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<drop noB="1"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiZhang</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					<!-- 我方 -->
					<unitOrder id="we1" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="摩卡" dpsMul="200" lifeMul="0.05" armsRange="sniperCicada$2,shotgunSkunkTwo$2" skillArr="feedback_hero_5,jumpNumAdd1,State_AddMove50" />
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="enemy5">
						<unit cnName="铁犬" unitType="boss" lifeMul="9999" dpsMul="1" skillArr="lifeBottleIconDog,IronDogMopTask"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">747,171,198,71</rect>
					<rect id="r_over">2948,308,70,109</rect>
					<rect id="r1">15,353,274,71</rect>
					<rect id="r2">2703,353,274,71</rect>
					<rect id="r3">1346,-64,300,72</rect>
					<rect id="r_back">-300,300,20,71</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">1476,933,60,38</rect>
					<rect label="addCharger">1453,16,60,38</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order>
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:摩卡</order>
							<order>level;taskTimingB:false</order>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
						</event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>createUnit:enemy5; r_over</order>
							<order>level;taskTimingB:true</order>
						</event>
					</group>
					<group>
						<event>
							<condition delay="0.01">task:state; mochaIronDog:complete</condition>
							<order>body:摩卡; noUnderHurt:true</order>
							<order>body:铁犬; stopAllClearState</order>
							<order>body:铁犬; addSkill:noAttackOrder</order>
							<order>body:铁犬; addSkill:State_noAllSkill</order>
							<order>body:铁犬; followBody:摩卡</order>
							<order>hideBossLifeBar</order>
							<order>say; startList:s3</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>level; win</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; 摩卡</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="beatIronDog">
				<!-- 关卡数据 -->
				<info enemyLv="99" noMoreB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" />
				<drop noB="1"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiZhang</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					<!-- 我方 -->
					<unitOrder id="we1" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="摩卡" dpsMul="200" lifeMul="0.05" armsRange="sniperCicada$2,shotgunSkunkTwo$2" skillArr="feedback_hero_5,jumpNumAdd1,State_AddMove50" dieGotoState="stru" aiOrder="followBodyAttack:我"/>
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="enemy5">
						<unit cnName="铁犬" unitType="boss" lifeMul="1" dpsMul="1" skillArr="IronDogMopTask"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">747,171,198,71</rect>
					<rect id="r_over">2948,308,70,109</rect>
					<rect id="r1">15,353,274,71</rect>
					<rect id="r2">2703,353,274,71</rect>
					<rect id="r3">1346,-64,300,72</rect>
					<rect id="r_back">-300,300,20,71</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">1476,933,60,38</rect>
					<rect label="addCharger">1453,16,60,38</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event>
							<condition></condition>
							<order>createUnit:we1; r_over</order>
							<order>createUnit:enemy5; r3</order>
							<order>level;taskTimingB:false</order>
						</event>
						<event>
							<condition delay="0.01">bodyEvent:die; 铁犬</condition>
							<order>body:铁犬; stopAllClearState</order>
							<order>body:铁犬; rebirth</order>
							<order>body:铁犬; noUnderHurt:true</order>
							<order>body:铁犬; addSkill:noAttackOrder</order>
							<order>body:铁犬; addSkill:State_noAllSkill</order>
							<order>body:摩卡; addSkill:noAttackOrder</order>
							<order>body:摩卡; addSkill:State_noAllSkill</order>
							<order>hideBossLifeBar</order>
							<order>say; startList:s1</order>
						</event>
						<event><![CDATA[对话完毕，铁犬隐身]]>
							<condition delay="1">say:listOver</condition>
							<order>hideBossLifeBar</order>
							<order>body:铁犬; playBreak:hideAttack</order>
						</event>
						<event>	
							<condition delay="0.4"></condition>
							<order>body:铁犬; toDel</order>
							<order>body:摩卡; rebirth</order>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					
				</eventG>
			</level>
		
		
		
			<level name="beatIronDog_otherRole">
				<!-- 关卡数据 -->
				<info enemyLv="99" allMoreB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" />
				<drop noB="1"/>
				<!-- 基本属性 -->
				<sceneLabel>BaiZhang</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					<!-- 我方 -->
					<unitOrder id="we1" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="摩卡" dpsMul="200" lifeMul="0.05" armsRange="sniperCicada$2,shotgunSkunkTwo$2" skillArr="feedback_hero_5,jumpNumAdd1,State_AddMove50" dieGotoState="stru" aiOrder="followBodyAttack:我"/>
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="enemy5">
						<unit cnName="铁犬" unitType="boss" lifeMul="0.2" dpsMul="0.3" skillArr="IronDogMopTask"/>
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">747,171,198,71</rect>
					<rect id="r_over">2948,308,70,109</rect>
					<rect id="r1">15,353,274,71</rect>
					<rect id="r2">2703,353,274,71</rect>
					<rect id="r3">1346,-64,300,72</rect>
					<rect id="r_back">-300,300,20,71</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">1476,933,60,38</rect>
					<rect label="addCharger">1453,16,60,38</rect>
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event>
							<condition></condition>
							<order>heroEverParasitic:striker</order>
							<order>createUnit:we1; r_over</order>
							<order>createUnit:enemy5; r3</order>
							<order>level;taskTimingB:false</order>
						</event>
						<event>
							<condition delay="0.01">bodyEvent:die; 铁犬</condition>
							<order>body:铁犬; stopAllClearState</order>
							<order>body:铁犬; rebirth</order>
							<order>body:铁犬; noUnderHurt:true</order>
							<order>body:铁犬; addSkill:noAttackOrder</order>
							<order>body:铁犬; addSkill:State_noAllSkill</order>
							<order>body:摩卡; addSkill:noAttackOrder</order>
							<order>body:摩卡; addSkill:State_noAllSkill</order>
							<order>hideBossLifeBar</order>
							<order>say; startList:s1</order>
						</event>
						<event><![CDATA[对话完毕，铁犬隐身]]>
							<condition delay="1">say:listOver</condition>
							<order>hideBossLifeBar</order>
							<order>body:铁犬; playBreak:hideAttack</order>
						</event>
						<event>	
							<condition delay="0.4"></condition>
							<order>body:铁犬; toDel</order>
							<order>body:摩卡; rebirth</order>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; 我</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
		</gather>
		
		
		<gather cnName="表哥的出走">
			<level name="wenjieAway">
				<!-- 关卡数据 -->
				<info enemyLv="99" diy="wotuBack" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<drop noB="1" />
				<!-- 基本属性 -->
				<sceneLabel>BaiSha</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					<!-- 我方 -->
					<unitOrder id="we1" camp="we"><!-- camp不填默认为enemy -->
						<unit cnName="文杰表哥" dpsMul="1000" lifeMul="0.01" armsRange="christmasGun$2" skillArr="groupLight_hero_7,hiding_hero_3,jumpNumAdd1,State_AddMove50" />
					</unitOrder>
					
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 敌方 -->
					<unitOrder id="enemy1">
						<unit cnName="僵尸突击兵" num="4" skillArr="dieAddCharger" />
						<unit cnName="僵尸狙击兵" num="1" lifeMul="2" dpsMul="0.2" skillArr="redEyes,dieAddCharger" armsRange="lightCone"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<unit cnName="僵尸暴枪兵" num="4" skillArr="dieAddCharger" />
						<unit cnName="僵尸狙击兵" num="1" lifeMul="2" dpsMul="0.2" skillArr="redEyes,dieAddCharger" armsRange="lightCone"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<unit cnName="僵尸突击兵" num="4" skillArr="dieAddCharger" />
						<unit cnName="防暴僵尸" num="1" lifeMul="3" skillArr="redEyes,dieAddCharger" armsRange="yearSheep"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="防暴僵尸" num="1" lifeMul="3" skillArr="redEyes,dieAddCharger" armsRange="yearSheep" />
						<unit cnName="僵尸狙击兵" num="1" lifeMul="2" dpsMul="0.2" skillArr="redEyes,dieAddCharger" armsRange="lightCone" />
					</unitOrder>
					<unitOrder id="enemy5">
						<unit cnName="狂战射手" num="1" lifeMul="3" dpsMul="6" skillArr="dieAddCharger" />
					</unitOrder>
				</unitG>
				
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1494,886,212,92 </rect>
					<rect id="r_over">3386,1084,48,100</rect>
					<rect id="r1">30,754,280,120</rect>
					<rect id="r2">1327,318,280,120</rect>
					<rect id="r3">3060,713,280,120</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">2156,638  ,118,64</rect>
					<rect label="addCharger">813,820,118,64</rect>
					
				</rectG>
				
				<!-- 事件集 -->
				<eventG>
					<group>
						<!-- 产生主角和藏师 -->
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order>
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:文杰表哥</order>
						</event>
						<event>
							<condition delay="1"></condition>
							<order>say; startList:s1</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
						</event>
						<![CDATA[]]>
						<event id="e1_1">
							<condition doNumber="3" orderChooseType="randomOne">liveEnemyNumber:less_1</condition>
							<order>createUnit:enemy1; r1</order>
							<order>createUnit:enemy1; r2</order>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e1_2">
							<condition doNumber="2" orderChooseType="randomOne">liveEnemyNumber:less_2</condition>
							<order>createUnit:enemy2; r1</order>
							<order>createUnit:enemy2; r2</order>
							<order>createUnit:enemy2; r3</order>
						</event>
						<event id="e1_3">
							<condition doNumber="2" orderChooseType="randomOne">liveEnemyNumber:less_2</condition>
							<order>createUnit:enemy3; r1</order>
							<order>createUnit:enemy3; r2</order>
							<order>createUnit:enemy3; r3</order>
						</event>
						<event id="e1_4">
							<condition doNumber="1" orderChooseType="randomOne">liveEnemyNumber:less_2</condition>
							<order>createUnit:enemy4; r1</order>
							<order>createUnit:enemy4; r2</order>
							<order>createUnit:enemy4; r3</order>
						</event>
						<event id="e1_5">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy5; r1</order>
							<order>createUnit:enemy5; r2</order>
							<order>createUnit:enemy5; r3</order>
						</event>
						<event id="e1_5">
							<condition delay="1">enemyNumber:less_1</condition>
							<order>say; startList:s2</order>
						</event>
						<event id="e_9">
							<condition delay="1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>more; openPartner3</order>
							<order>alert:yes; 队友的上场数量增加！</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; 文杰表哥</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
		</gather>
		
	</father>
</data>