<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="partsCoin" cnName="零件券" priceType="coin" labelArr="all">
		<goods defineLabel="lshapedParts_1" dataType="parts" price="0" 		buyLimitNum="9999"/>
		<goods defineLabel="shockParts_1" dataType="parts" price="0" 		buyLimitNum="9999"/>
		<goods defineLabel="hardeningParts_1" dataType="parts" price="0" 		buyLimitNum="9999"/>
		<goods defineLabel="speedParts_1" dataType="parts" price="0" 		buyLimitNum="9999"/>
		<goods defineLabel="downSpeedParts_1" dataType="parts" price="0" 		buyLimitNum="9999"/>
		
		<goods defineLabel="twoShootParts_1" dataType="parts" price="0" 		buyLimitNum="9999"/>
		<goods defineLabel="eleParts_1" dataType="parts" price="0" 		buyLimitNum="9999"/>
		<goods defineLabel="redArmsParts_1" dataType="parts" price="0" 		buyLimitNum="9999"/>
		<![CDATA[
		<goods defineLabel="uidpsParts_1" dataType="parts" price="40" 		buyLimitNum="1"/>
		]]>
		
		<goods defineLabel="penbodyParts_1" dataType="parts" price="0" 		buyLimitNum="9999"/>
		<goods defineLabel="digWallParts_1" dataType="parts" price="0" 		buyLimitNum="9999"/>
		
		
		<goods defineLabel="degaussingParts_1" dataType="parts" price="0" 		buyLimitNum="9999"/>
		<goods defineLabel="angleCtrlParts_1" dataType="parts" price="0" 		buyLimitNum="9999"/>
		<goods defineLabel="fireParts_1" dataType="parts" price="0" 		buyLimitNum="9999"/>
		<goods defineLabel="electricParts_1" dataType="parts" price="0" 		buyLimitNum="9999"/>
	</father>
</data>