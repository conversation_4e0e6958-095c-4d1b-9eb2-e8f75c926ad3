<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father type="zombie" cnName="童灵尸">
		<bullet cnName="童灵尸-雪球">
			<name>SnowThinShoot</name>
			<cnName>童灵尸-雪球</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.8</attackGap>
			<attackDelay>0.62</attackDelay>
			<bulletAngle>185</bulletAngle>
			<bulletAngleRange>40</bulletAngleRange>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>					
								
			<shootAngle>0</shootAngle>					
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-62,-64</shootPoint>
			<bulletSpeed>15</bulletSpeed>
			<gravity>0.3</gravity>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1">SnowThin/bullet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/snowHit" raNum="8" con="add">bulletHitEffect/snowHit</hitImgUrl>
			<hitFloorImgUrl  raNum="8" con="add">bulletHitEffect/snowHit</hitFloorImgUrl>
			<smokeImgUrl con="filter" raNum="30">SnowThin/smoke</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="童灵尸-旋转雪球">
			<name>SnowThinRotate</name>
			<cnName>童灵尸-旋转雪球</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.6</attackGap>
			<attackDelay>0.6</attackDelay>
			<bulletAngle>135</bulletAngle>		
			<shootNum>5</shootNum>					
			<shootGap>0.15</shootGap>						
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-23,-117</shootPoint>
			<bulletSpeed>20</bulletSpeed>
			
			<boomD  selfB="1" radius="130"/>
			<bounceD glueFloorB="1" />
			<penetrationNum>-1</penetrationNum>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1">SnowThin/bullet2</bulletImgUrl>
			<hitImgUrl soundUrl="sound/snowHit" raNum="1" con="add">bulletHitEffect/snowBigHit</hitImgUrl>
			<hitFloorImgUrl  raNum="8" con="add">bulletHitEffect/snowHit</hitFloorImgUrl>
			<smokeImgUrl con="filter" raNum="30">SnowThin/smoke</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
	</father>	
	<father type="zombie" cnName="关东尸">
		<bullet cnName="关东尸-飞刀">
			<name>SnowFattyShoot</name>
			<cnName>关东尸-飞刀</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.1</attackGap>
			<attackDelay>0.46</attackDelay>
			<bulletAngle>185</bulletAngle>
			<bulletAngleRange>50</bulletAngleRange>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>					
								
			<shootAngle>0</shootAngle>					
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-81,-95</shootPoint>
			<bulletSpeed>30</bulletSpeed>
			<gravity>0.7</gravity>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1">SnowFatty/bullet</bulletImgUrl>
			<hitImgUrl con="add" soundUrl="sound/vehicle_hit1">bladeHitEffect/blood</hitImgUrl>
			<hitFloorImgUrl con="add" soundUrl="sound/vehicle_hit1" soundVolume="0.4">bladeHitEffect/blood</hitFloorImgUrl>
		</bullet>
		
		<bullet cnName="关东尸-冲撞前飞刀">
			<name>SnowFattySprint</name>
			<cnName>关东尸千斤顶</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>50</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.1</attackGap>
			<attackDelay>0.46</attackDelay>
			<bulletAngle>185</bulletAngle>
			<bulletAngleRange>90</bulletAngleRange>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>					
								
			<shootAngle>0</shootAngle>					
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,-75</shootPoint>
			<bulletSpeed>30</bulletSpeed>
			<penetrationGap>1000</penetrationGap>
			<skillArr>SnowFattySprintHit,hitBloodGreen</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1">SnowFatty/bullet</bulletImgUrl>
			<hitImgUrl con="add" soundUrl="sound/vehicle_hit1">bladeHitEffect/blood</hitImgUrl>
			<hitFloorImgUrl con="add" soundUrl="sound/vehicle_hit1" soundVolume="0.4">bladeHitEffect/blood</hitFloorImgUrl>
			<smokeImgUrl con="filter" raNum="1">SnowFatty/bullet</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
	</father>
	
	<father type="zombie" cnName="野帝">
		<bullet cnName="野帝-雪球">
			<name>IceManShoot1</name>
			<cnName>野帝-雪球</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.3</attackGap>
			<attackDelay>0.72</attackDelay>
			<bulletAngle>165</bulletAngle>
			<bulletAngleRange>30</bulletAngleRange>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>					
								
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-232,-128</shootPoint>
			<bulletSpeed>15</bulletSpeed>
			<penetrationGap>1000</penetrationGap>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1">IceMan/bullet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/snowHit" raNum="8">bulletHitEffect/snowHit</hitImgUrl>
			<hitFloorImgUrl raNum="8">bulletHitEffect/snowHit</hitFloorImgUrl>
			<smokeImgUrl con="filter" raNum="30">IceMan/smoke</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="野帝-雪球2">
			<name>IceManShoot2</name>
			<cnName>野帝-雪球2</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>15</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.7</attackGap>
			<attackDelay>0.72</attackDelay>
			<bulletAngle>165</bulletAngle>
			<bulletAngleRange>30</bulletAngleRange>
			<bulletNum>1</bulletNum>				
			<shootNum>3</shootNum>					
			<shootGap>0.19</shootGap>					
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-214,-138</shootPoint>
			<bulletSpeed>15</bulletSpeed>
			<penetrationGap>1000</penetrationGap>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1">IceMan/bullet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/snowHit" raNum="8">bulletHitEffect/snowHit</hitImgUrl>
			<hitFloorImgUrl raNum="8">bulletHitEffect/snowHit</hitFloorImgUrl>
			<smokeImgUrl con="filter" raNum="30">IceMan/smoke</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		
		<bullet cnName="野帝-踢爆">
			<name>IceManKick</name>
			<cnName>野帝-踢爆</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>9</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>70</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>2</attackGap>
			<attackDelay>0.8</attackDelay>
			<bulletAngle>-170</bulletAngle>
			<bulletNum>1</bulletNum>				
			<shootNum>4</shootNum>					
			<shootGap>0.1</shootGap>					
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-156,-33</shootPoint>
			<bulletSpeed>25</bulletSpeed>
			<penetrationGap>1000</penetrationGap>
			<skillArr>IceManKickHit</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="2">IceMan/kickBullet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/snowHit" raNum="8">bulletHitEffect/snowHit</hitImgUrl>
		</bullet>
		
		<bullet cnName="野帝-暴怒一击">
			<name>IceManShake</name>
			<cnName>野帝-暴怒一击</cnName>
			<!--伤害属性------------------------------------------------------------ -->
			<hurtRatio>3</hurtRatio>
			<attackType>holy</attackType>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>0.01</bulletLife>
			<bulletWidth>100</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0</attackGap>
			<attackDelay>0</attackDelay>
			<bulletAngle>270</bulletAngle>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>					
								
			<shootAngle>0</shootAngle>					
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<twoHitGap>0.2</twoHitGap>
			<twoHitSameNameB>1</twoHitSameNameB>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>0,0</shootPoint>
			<bulletSpeed>0</bulletSpeed>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<imgClearDelay>0.24</imgClearDelay>
			<bulletImgUrl>IceMan/boom</bulletImgUrl>
			<hitImgUrl con="add" soundUrl="sound/snowHit">bulletHitEffect/energy</hitImgUrl>
		</bullet>
	</father>	
	<father type="zombie" cnName="其他">
		<bullet cnName="飓风">
			<name>snowWind</name>
			<cnName>飓风</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>1</shakeAngle>
			<bulletLife>999999999</bulletLife>
			<bulletWidth>60</bulletWidth>
			<hitType>rect</hitType>
			<noHitB>1</noHitB>
			<!--攻击时的属性------------------------------------------------------------ -->
			<bulletAngle>180</bulletAngle>
			<bulletNum>1</bulletNum>				
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationNum>999</penetrationNum>
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>1</bulletSpeed>
			<followD value="1"/>	<!-- 跟踪 -->
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1">generalEffect/windEffect</bulletImgUrl>
		</bullet>
	</father>
</data>