<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father type="flamer" cnName="喷火器">
		<bullet name="洪荒"  color="black" composeLv="95" chipNum="123">
			<name>waterFlamer</name><cnName>洪荒</cnName>
			<dpsMul>2.4</dpsMul>
			<uiDpsMul>2.5</uiDpsMul>
			<!--基本-->
			<capacity>100,120</capacity>
			<attackGap>0.15</attackGap>
			<reloadGap>2.5,3</reloadGap>
			<shakeAngle>3,5</shakeAngle>
			<bulletWidth>25</bulletWidth>
			<bulletNum>1</bulletNum>
			<bulletShakeWidth>0,0</bulletShakeWidth>
			<beatBack>3</beatBack>
			<targetShakeValue>5</targetShakeValue>
			<!--武器属性------------------------------------------------------------ -->
			<armsArmMul>0.5</armsArmMul><shootShakeAngle>5</shootShakeAngle><shootRecoil>3</shootRecoil><screenShakeValue>4</screenShakeValue>
			<!--基本属性------------------------------------------------------------ -->
			<noShakeTime>0.5</noShakeTime><bulletLife>1</bulletLife>
			<hitType>rect</hitType>
			<!--运动属性------------------------------------------------------------ -->	
			<speedD random="0.2" max="40" min="10" a="-30" />
			<bulletSpeed>40</bulletSpeed>
			<gravity>1</gravity>
			<!--特殊------------------------------------------------------------ -->	
			<oneHitBodyB>1</oneHitBodyB>
			<godSkillArr>Hit_imploding_godArmsSkill,waterFlamer_ArmsSkill</godSkillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<shootSoundUrl>specialGun/waterShoot</shootSoundUrl>
			<bulletImgUrl raNum="30">bullet/waterBullet</bulletImgUrl>
			<smokeImgUrl raNum="30" con="filter">bullet/waterSmoke</smokeImgUrl>
			<hitImgUrl raNum="30">bulletHitEffect/smoke_motion</hitImgUrl><!-- 子弹图像【必备】 -->
			<hitFloorImgUrl raNum="30">bulletHitEffect/smoke_motion</hitFloorImgUrl><!-- 子弹图像【必备】 -->
			<fireImgType>no</fireImgType>
			<!--图像范围------------------------------------------------------------ -->
			<bodyImgRange>specialGun/waterFlamer</bodyImgRange><bulletImgRange>specialGun/bullet</bulletImgRange>
		</bullet>
	</father>
	
	<father name="godArmsSkill" cnName="神级武器技能">
		<skill index="1" name="湿身">
			<name>waterFlamer_ArmsSkill</name><noRandomListB>1</noRandomListB>
			<cnName>湿身</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>underHurtMul</effectType>
			<passiveSkillArr>waterFlamer_sniper</passiveSkillArr>
			<mul>1.1</mul>
			<duration>2</duration>
			<stateEffectImg partType="head" con="add">generalEffect/waterFace</stateEffectImg>
			
			<description>使目标防御力降低[mul-1]，同时受到狙击枪的伤害提升50%。</description>
		</skill>
				<skill index="1" name="湿身-狙击枪伤害提升">
					<name>waterFlamer_sniper</name><noRandomListB>1</noRandomListB>
					<cnName>湿身-狙击枪伤害提升</cnName>
					<!--触发条件与目标------------------------------------------------------------ -->
					<conditionType>passive</conditionType>
					<condition>underHit</condition>
					<target>target</target>
					<!--效果------------------------------------------------------------ -->
					<addType>instant</addType>
					<effectType>changeHurt_gunType</effectType>
					<valueString>sniper</valueString>
					<mul>1.5</mul>
					<duration>2</duration>
				</skill>
	</father>
	
</data>