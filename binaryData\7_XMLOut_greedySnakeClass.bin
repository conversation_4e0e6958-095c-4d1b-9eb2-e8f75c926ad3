<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father type="pistol" cnName="手枪">
		<bullet name="greedySnake" color="black" dropLevelArr="999"  evoB="0" composeLv="97" chipB="1" chipNum="100">
			<name>greedySnake</name>
			<cnName>贪吃蛇</cnName>
			<!--基本-->
			<capacity>30</capacity>
			<attackGap>0.4</attackGap>
			<reloadGap>2.5</reloadGap>
			<shakeAngle>0</shakeAngle>
			<bulletWidth>40</bulletWidth>
			<bulletNum>1</bulletNum>				
			<!--特殊------------------------------------------------------------ -->
			<twoShootPro>0</twoShootPro>
			<penetrationNum>10</penetrationNum>
			<penetrationGap>1000</penetrationGap>
			<bounceD floor="0" body="0"/>	<!-- 反弹 -->
			
			<skillArr>Hit_SlowMove_ArmsSkill,Hit_disabled_ArmsSkill</skillArr>
			<godSkillArr>editBulletPath,greedySnakeSkill</godSkillArr>
			<!--武器属性------------------------------------------------------------ -->
			<dpsMul>0.4</dpsMul>
			<uiDpsMul>0.5</uiDpsMul>
			<hurtRatio>1</hurtRatio>
			<gunNum>1</gunNum>
			<armsArmMul>1.05</armsArmMul>
			<armsWeight>5</armsWeight>
			<upValue>0</upValue>
			<shootShakeAngle>15</shootShakeAngle>
			<shootRecoil>10</shootRecoil>
			<screenShakeValue>15</screenShakeValue>
			<oneHitBodyB>1</oneHitBodyB>
			<!--基本属性------------------------------------------------------------ -->
			<bulletSpeed>20</bulletSpeed>
			<bulletLife>5</bulletLife>
			<hitType>rect</hitType>
			<!--特殊------------------------------------------------------------ -->
			<!--图像动画属性------------------------------------------------------------ -->.
			<shootSoundUrl>specialGun/greedySnakeSound</shootSoundUrl>
			<flipX>1</flipX>
			<lineD editB="1" />
			<bulletImgUrl raNum="30" con="filter">specialGun/greedySnakeBullet</bulletImgUrl>
			<hitImgUrl>bulletHitEffect/fitHit</hitImgUrl><!-- 子弹图像【必备】 -->
			<!--图像范围------------------------------------------------------------ -->
			<bodyImgRange>specialGun/greedySnake</bodyImgRange>
			<bulletImgRange>specialGun/bullet</bulletImgRange>
			<description>活动获得</description>
		</bullet>
	</father>
	<father name="godArmsSkill" cnName="神级武器技能">
		<skill name="阻挡">
			<name>greedySnakeSkill</name><noRandomListB>1</noRandomListB>
			<cnName>阻挡</cnName><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB><noBeClearB>1</noBeClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>greedySnakeSkill</effectType>
			<mul>0</mul>
			<secMul>5</secMul>
			<duration>0.4</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg con="add" soundUrl="sound/paralysis_enemy_hit">skillEffect/paralysis_enemy</stateEffectImg>
			<description>击中怪物时，让它无法移动，但也会大幅提升它的防御力。无视技能免疫。</description>
		</skill>
	</father>
</data>