<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="wilder">
		<body index="0" name="爆骷S" shell="compound">
		
			<name>BoomSkullS</name>
			<cnName>爆骷S</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/enemy/BoomSkullS.swf</swfUrl>
			<headIconUrl>IconGather/PetBoomSkullS</headIconUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<rosRatio>1</rosRatio>
			<headHurtMul>0.5</headHurtMul>
			<showLevel>999</showLevel>
			<!-- 图像 -->
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<dieJumpMul>0</dieJumpMul>
			<imgType>normal</imgType>
			<imgArr>
				stand,move,die1
				,followAttack,paraAttack,generalAttack,boxAttack,boomAttack
			</imgArr>
			<lifeBarExtraHeight>-50</lifeBarExtraHeight>
			<!-- 碰撞体积 -->
			<hitRect>-30,-88,60,88</hitRect>
			<!-- 运动 -->
			<maxVx>7</maxVx>
			<motionState>fly</motionState>
			<flyUseSpiderB>1</flyUseSpiderB>
			<flyType>tween</flyType>
			<!-- 技能 -->
			<bossSkillArr>State_InvincibleThrough,teleport_BoomSkullS,desertedHalo_BoomSkullS</bossSkillArr>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt cd="2">
					<imgLabel>generalAttack</imgLabel>
					<bulletLabel>BoomSkullS_general</bulletLabel>
					<grapRect>-9999,-9999,19999,19999</grapRect>
					<hurtRatio>0.000000001</hurtRatio>
					<hurtMul>0.2</hurtMul>
					<attackType>holy</attackType>
				</hurt>
				<hurt cd="3">
					<imgLabel>boomAttack</imgLabel>
					<bulletLabel>BoomSkullS_boom</bulletLabel>
					<grapRect>-9999,-9999,19999,19999</grapRect>
					<hurtRatio>0.000000001</hurtRatio>
					<hurtMul>0.35</hurtMul>
					<attackType>holy</attackType>
				</hurt>
				<hurt>
					<imgLabel>followAttack</imgLabel>
					<bulletLabel>BoomSkullS_follow</bulletLabel>
					<grapRect>-9999,-9999,19999,19999</grapRect>
					<hurtRatio>0.000000001</hurtRatio>
					<hurtMul>0.2</hurtMul>
					<attackType>holy</attackType>
				</hurt>
				<hurt>
					<imgLabel>paraAttack</imgLabel>
					<bulletLabel>BoomSkullS_para</bulletLabel>
					<grapRect>-9999,-9999,19999,19999</grapRect>
					<hurtRatio>0.000000001</hurtRatio>
					<hurtMul>0.35</hurtMul>
					<attackType>holy</attackType>
				</hurt>
				<hurt cd="8">
					<imgLabel>boxAttack</imgLabel>
					<bulletLabel>BoomSkullS_box</bulletLabel>
					<grapRect>-9999,-9999,19999,19999</grapRect>
					<hurtRatio>0.000000001</hurtRatio>
					<hurtMul>0.1</hurtMul>
					<attackType>holy</attackType>
				</hurt>
			</hurtArr>
		</body>
	</father>
	<father name="enemy">	
		<bullet>
			<name>BoomSkullS_general</name>
			<cnName>爆骷S-普通导弹</cnName><noMagneticB>1</noMagneticB><shootRecoil>0.015</shootRecoil>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>4</shakeAngle>
			<bulletLife>15</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.8</attackGap>
			<attackDelay>0.5</attackDelay>
			<bulletAngle>180</bulletAngle>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-173,-65</shootPoint>
			<bulletSpeed>12</bulletSpeed>
			<speedD random="0.2"/>
			<boomD selfB="1" hurtMul="0" noExcludeBodyB="1" haveBodyHitEffectB="1"/>
			<bindingD cnName="空单位" lifeMul="0.003" skillArr="BoomSkullSBulletDie,State_noAiFind" />
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="2">BoomSkullS/generalBullet</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/microBoom2"  shake="3,0.4,13">boomEffect/boom2</hitImgUrl>
		</bullet>
		<bullet>
			<name>BoomSkullS_boom</name>
			<cnName>爆骷S-轰炸导弹</cnName><noMagneticB>1</noMagneticB><shootRecoil>0.005</shootRecoil>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>4</shakeAngle>
			<bulletLife>15</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.8</attackGap>
			<attackDelay>0.5</attackDelay>
			<bulletAngle>180</bulletAngle>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-173,-65</shootPoint>
			<bulletSpeed>12</bulletSpeed>
			<speedD random="0.3"/>
			<boomD selfB="1" floorB="1" bodyB="1" radius="120" noExcludeBodyB="1" haveBodyHitEffectB="1"/>
			<bindingD cnName="空单位" lifeMul="0.01" skillArr="BoomSkullSBulletDie,State_noAiFind" />
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="2">BoomSkullS/boomBullet</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/bigBoom"  shake="5,0.4,13">boomEffect/bigCircle</hitImgUrl>
		</bullet>
		
		
		<bullet>
			<name>BoomSkullS_box</name>
			<cnName>爆骷S-箱子导弹</cnName><noMagneticB>1</noMagneticB>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>15</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.8</attackGap>
			<attackDelay>0.6</attackDelay>
			<bulletAngle>-167</bulletAngle>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<penetrationNum>-1</penetrationNum>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-176,-123</shootPoint>
			<bulletSpeed>7</bulletSpeed>
			<boomD selfB="1" hurtMul="0" noExcludeBodyB="1" haveBodyHitEffectB="1"/>
			<bindingD cnName="空单位" lifeMul="0.02" skillArr="BoomSkullSBulletDie,State_noAiFind"/>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="2">BoomSkullS/boxBullet</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/boom"  shake="3,0.4,13">boomEffect/midWood</hitImgUrl>
		</bullet>
		
		<bullet>
			<name>BoomSkullS_follow</name>
			<cnName>爆骷S-跟踪导弹</cnName><noMagneticB>1</noMagneticB><shootRecoil>0.03</shootRecoil>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>25</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.8</attackGap>
			<attackDelay>0.63</attackDelay>
			<bulletAngle>-141</bulletAngle>
			<!--特殊属性------------------------------------------------------------ -->	
			<followD value="0.4" delay="0.7" />	<!-- 跟踪 -->
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-133,-175</shootPoint>
			<bulletSpeed>20</bulletSpeed>
			<speedD min="10" max="20" a="-5"/>
			<boomD selfB="1" hurtMul="0" noExcludeBodyB="1" haveBodyHitEffectB="1"/>
			<bindingD cnName="空单位" lifeMul="0.004" skillArr="BoomSkullSBulletDie,State_noAiFind"/>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">BoomSkullS/bullet1</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom2"  shake="3,0.4,13">boomEffect/boom2</hitImgUrl>
			<smokeImgUrl con="filter" raNum="30">bullet/fireballSmoke</smokeImgUrl>
		</bullet>
		
		<bullet>
			<name>BoomSkullS_para</name>
			<cnName>爆骷S-抛物线导弹</cnName><noMagneticB>1</noMagneticB><shootRecoil>0.06</shootRecoil>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>15</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.8</attackGap>
			<attackDelay>0.63</attackDelay>
			<bulletAngle>-143</bulletAngle>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-146,-150</shootPoint>
			<bulletSpeed>13</bulletSpeed>
			<speedD random="0.1"/>
			<boomD selfB="1" floorB="1" bodyB="1" radius="200" noExcludeBodyB="1" haveBodyHitEffectB="1"/>
			<bindingD cnName="空单位" lifeMul="0.008" skillArr="BoomSkullSBulletDie,State_noAiFind"/>
			<gravity>0.3</gravity>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">BoomSkullS/paraBullet</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/bigBoom"  shake="5,0.4,13">boomEffect/bigCircle</hitImgUrl>
			<smokeImgUrl con="filter" raNum="30">bullet/fireballSmoke</smokeImgUrl>
		</bullet>
		
	</father>
	<father name="enemy">
		<skill>
			<name>BoomSkullSBulletDie</name>
			<cnName>导弹受到伤害</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>die</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>BoomSkullSBulletDie</effectType>
		</skill>
		<skill>
			<name>teleport_BoomSkullS</name>
			<cnName>瞬移</cnName>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>2</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>200</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>teleport_BoomSkullS</effectType>
			<!--图像------------------------------------------------------------ --> 
			<meEffectImg soundUrl="sound/teleport_enemy" con="add">lightEffect/basinShow</meEffectImg>
		</skill>
		<skill><!-- dps -->
			<name>desertedHalo_BoomSkullS</name><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<cnName>导弹系统</cnName><noCopyB>1</noCopyB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>desertedHalo_BoomSkullS</effectType>
			<duration>2</duration>
			<range>99999</range>
			<!--图像------------------------------------------------------------ -->
			<description>爆骷S处于虚无状态，不受任何攻击，并向敌人发射多种导弹。玩家击爆导弹才能使爆骷S受到伤害。</description>
		</skill>
	</father>
</data>