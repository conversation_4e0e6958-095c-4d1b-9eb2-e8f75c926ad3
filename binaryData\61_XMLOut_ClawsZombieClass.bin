<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="enemy">
		<body shell="compound">
			<name>ClawsZombie</name>
			<cnName>利爪僵尸</cnName><headIconUrl>IconGather/ClawsZombie</headIconUrl>
			<raceType>zombies</raceType>
			<swfUrl>swf/enemy/ClawsZombie310.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<showLevel>999</showLevel>
			<!-- 图像 -->
			<imgArr>
				stand,move,run
				,normalAttack,shootAttack
				,comboAttack,dartleAttack,shakeAttack
				,hurt1,hurt2,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<bossImgArr>comboAttack,dartle<PERSON>ttack,shakeAttack</bossImgArr>
			<!-- 碰撞体积 -->
			<hitRect>-14,-76,28,76</hitRect>
			<!-- 运动 -->
			<maxJumpNum>2</maxJumpNum>
			<maxVx>7</maxVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel></extraAIClassLabel>
			<avtiveSkillCdOverT>0</avtiveSkillCdOverT>
			<!-- 技能 -->
			<skillArr>noBounce_enemy,reflectiveShell,reverseHurt_enemy</skillArr>
			<bossSkillArr>clawsCombo,clawsDartle,clawsShake,immune,treater_knights,magneticField_enemy,bladeShield,invincibleEmp</bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack</imgLabel><cn>爪击</cn>
					<hurtRatio>1.2</hurtRatio>
					<shakeValue>4</shakeValue>
					<attackType>direct</attackType>
					<skillArr></skillArr>
					<hitImgUrl name="clawHit" />
				</hurt>
				<hurt>
					<imgLabel>shootAttack</imgLabel><cn>火爪</cn>
					<bulletLabel>ClawsZombie_1</bulletLabel>
					<grapRect>-600,-132,450,133</grapRect>
					<hurtRatio>1.5</hurtRatio>
					<attackType>holy</attackType>
				</hurt>
				<hurt>
					<imgLabel>comboAttack</imgLabel><cn>侵略之爪</cn><noAiChooseB>1</noAiChooseB>
					<hurtRatio>2</hurtRatio>
					<shakeValue>4</shakeValue>
					<attackType>holy</attackType>
					<skillArr></skillArr>
					<hitImgUrl name="clawHit" />
				</hurt>
				<hurt>
					<imgLabel>shakeAttack</imgLabel><cn>侵占之跃</cn><noAiChooseB>1</noAiChooseB>
					<hurtRatio>10</hurtRatio>
					<shakeValue>4</shakeValue>
					<attackType>holy</attackType>
					<skillArr>pullAttackHammer</skillArr>
					<hitImgUrl name="clawHit" />
				</hurt>
				<hurt>
					<imgLabel>dartleAttack</imgLabel><cn>侵袭之火</cn><noAiChooseB>1</noAiChooseB>
					<bulletLabel>clawsDartleBullet</bulletLabel>
					<grapRect>-600,-132,450,133</grapRect>
					<hurtRatio>0.5</hurtRatio>
					<shakeValue>4</shakeValue>
					<attackType>holy</attackType>
				</hurt>
			</hurtArr>
		</body>
		
		
		
		<bullet>
			<name>ClawsZombie_1</name>
			<cnName>火爪</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>3</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.8</attackGap>
			<attackDelay>0.6</attackDelay>
			<bulletAngle>-179</bulletAngle>
			<bulletAngleRange>50</bulletAngleRange>
			<shootPoint>-111,-78</shootPoint>
			<bulletSpeed>18</bulletSpeed>
			<penetrationGap>1000</penetrationGap>
			<skillArr></skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl name="clawsBullet"/>
			<hitImgUrl name="rocketCate_hit"/>
		</bullet>
		<bullet>
			<name>clawsDartleBullet</name>
			<cnName>侵袭之火</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>4</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1.6</attackGap>
			<attackDelay>0.5</attackDelay>
			<bulletAngle>-179</bulletAngle>
			<bulletAngleRange>50</bulletAngleRange>
			<bulletNum>10</bulletNum>
			<shootAngle>60</shootAngle>
			<shootGap>0.26</shootGap>
			<shootNum>5</shootNum>
			<shootPoint>-111,-78</shootPoint>
			<bulletSpeed>15</bulletSpeed>
			<followD value="2" delay="0.2" maxTime="1.5" />	 
			
			<penetrationGap>1000</penetrationGap>
			<speedD random="0.3"/>
			<skillArr></skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl name="clawsBullet"/>
			<smokeImgUrl con="filter" raNum="30">bullet/fireballSmoke</smokeImgUrl>
			<hitImgUrl name="rocketCate_hit"/>
		</bullet>
		
		
		
		
		<skill>
			<name>acidicBlood</name>
			<cnName>侵蚀之血</cnName>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHit</condition>
			<otherConditionArr>hurtArmsBulletNumMore</otherConditionArr>
			<conditionRange>1</conditionRange>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>setBulletNum</effectType>
			<value>0.3</value>
			<mul>2</mul>
			<duration>3</duration>
			<stateEffectImg name="gunSmokeSmall" />
			<description>受到散射枪支攻击后，侵蚀目标70%的散射子弹，持续[duration]秒。</description>
		</skill>
		<skill>
			<name>clawsCombo</name>
			<cnName>侵略之爪</cnName><iconUrl36></iconUrl36><ignoreNoSkillB>1</ignoreNoSkillB>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<cd>11</cd>
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>noAttackImg</otherConditionArr>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>clawsCombo</effectType>
			<duration>1.9</duration>
			<!--技能链接或者需要添加的被动技能------------------------------------------------------------ --> 
			<passiveSkillArr>clawsComboSlow</passiveSkillArr>
			<!--图像------------------------------------------------------------ --> 
			<meActionLabel>comboAttack</meActionLabel>
			<description>利爪僵尸发出侵略之爪，连续对敌人造成重大伤害。</description>
		</skill>
				<skill>
					<name>clawsComboSlow</name>
					<cnName>侵略之爪-全体减速</cnName><ignoreNoSkillB>1</ignoreNoSkillB>
					<noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
					<conditionType>active</conditionType>
					<target>me,range,enemy</target>
					<!--效果------------------------------------------------------------ -->
					<addType>state</addType>
					<effectType>moveSpeed</effectType>
					<value>0</value>
					<mul>0.4</mul>
					<duration>2</duration>
					<range>999999</range>
					<!--图像------------------------------------------------------------ -->
					<stateEffectImg partType="2foot">skillEffect/disabled_enemy</stateEffectImg>
				</skill>
		<skill>
			<name>clawsShake</name>
			<cnName>侵占之跃</cnName><iconUrl36></iconUrl36><ignoreNoSkillB>1</ignoreNoSkillB>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<cd>15</cd>
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>noAttackImg</otherConditionArr>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>clawsShake</effectType>
			<duration>1.2</duration>
			<!--图像------------------------------------------------------------ --> 
			<meActionLabel>shakeAttack</meActionLabel>
			<otherEffectImg con="add" >skillEffect/apertureBig</otherEffectImg>
			<description>利爪僵尸一跃而起，对敌人发起重击，造成重大伤害，并眩晕敌人。</description>
		</skill>
		<skill>
			<name>clawsDartle</name>
			<cnName>侵袭之火</cnName><iconUrl36></iconUrl36><ignoreNoSkillB>1</ignoreNoSkillB>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<cd>12</cd>
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>noAttackImg</otherConditionArr>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>invincible</effectType>
			<duration>1.6</duration>
			<!--图像------------------------------------------------------------ --> 
			<meActionLabel>dartleAttack</meActionLabel>
			<description>利爪僵尸瞬间发出几十条跟踪火焰攻击敌人。</description>
		</skill>
	</father>
</data>