<?xml version="1.0" encoding="utf-8" ?>
<data>
	<![CDATA[六周年已添加]]>
	<father name="enemy">
		<body name="末日坦克" shell="metal">
			<name>LastdayTank</name><showLevel>999</showLevel>
			<cnName>末日坦克</cnName><editD raIfNoMapB="1"/>
			<raceType>robot</raceType>
			<swfUrl>swf/enemy/LastdayTank.swf</swfUrl>
			<otherUnitCnNameArr>末日坦克部件</otherUnitCnNameArr>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<rosRatio>1</rosRatio>
			<headHurtMul>0.5</headHurtMul>
			<!-- 图像 -->
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<dieJumpMul>0</dieJumpMul>
			<imgType>normal</imgType>
			<imgArr>
				stand,move,die1
				,big3A<PERSON>ck,bigAttack,missleAttack,viking<PERSON>ttack
			</imgArr>
			<lifeBarExtraHeight>-40</lifeBarExtraHeight>
			<!-- 碰撞体积 -->
			<hitRect>-30,-88,60,88</hitRect>
			<!-- 运动 -->
			<motionD F_G="0.6" jumpDelayT="0.2" F_I="0.2" F_F="0.9" moveWhenVB="1" />
			<maxVx>10</maxVx>
			<maxJumpNum>0</maxJumpNum>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel>LastdayTank_AIExtra</extraAIClassLabel>
			<!-- 技能 -->
			<bossSkillArr>LastdayAttackDefend,LastdayHit,LastdayAir,LastdaySpellImmunity,BallLightningHurt,ruleRangeIron,rigidBody_enemy,noSpeedReduce</bossSkillArr>
			<preBulletArr>LastdayBigChild</preBulletArr>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>stand</imgLabel>
					<hurtRatio>2</hurtRatio>
					<attackType>holy</attackType>
					<shakeValue>10</shakeValue><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit1">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>move</imgLabel>
					<hurtRatio>2</hurtRatio>
					<attackType>holy</attackType>
					<shakeValue>10</shakeValue><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit1">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt cd="1.35" info="不加入ai选择">
					<imgLabel>bigAttack</imgLabel>
					<bulletLabel>LastdayBig</bulletLabel>
					<grapRect>-776,-500,766,500</grapRect><!-- <grapRect>-812,-422,364,525</grapRect> -->
					<hurtRatio>3</hurtRatio>
					<attackType>holy</attackType>
				</hurt>
				<hurt cd="1.7" info="不加入ai选择">
					<imgLabel>missleAttack</imgLabel>
					<bulletLabel>LastdayMissle</bulletLabel>
					<grapRect>-776,-500,766,500</grapRect><!-- <grapRect>-550,-545,732,376</grapRect> -->
					<hurtRatio>3</hurtRatio>
					<attackType>holy</attackType>
				</hurt>
				<hurt cd="1" info="不加入ai选择">
					<imgLabel>vikingAttack</imgLabel>
					<bulletLabel>LastdayViking</bulletLabel>
					<grapRect>-776,-500,766,500</grapRect><!-- <grapRect>-528,-291,243,346</grapRect> -->
					<hurtRatio>0.6</hurtRatio>
					<attackType>holy</attackType>
				</hurt>
				<hurt cd="1.5" info="不加入ai选择">
					<imgLabel>big3Attack</imgLabel>
					<bulletLabel>LastdayBig3</bulletLabel>
					<grapRect>-776,-500,766,500</grapRect><!-- <grapRect>-1011,-186,569,200</grapRect> -->
					<hurtMul>0.5</hurtMul>
					<attackType>holy</attackType>
				</hurt>
			</hurtArr>
		</body>
					
	</father>
	<father name="other" cnName="其他">
		<body shell="other">
			<name>LastdayTankUnder</name>
			<cnName>末日坦克部件</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/enemy/LastdayTankUnder.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<dieJumpMul>0</dieJumpMul>
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<imgType>normal</imgType>
			<imgArr>stand,die1</imgArr>
			<hitRect>-1,-1,2,2</hitRect>
			<motionState>fly</motionState><maxJumpNum>0</maxJumpNum><maxVx>0</maxVx>
			<defaultAiOrder>no</defaultAiOrder><nextAttackTime>0</nextAttackTime>
			<skillArr>State_SpellImmunity,rigidBody_enemy,LastdayPartUnder,LastdayPartDie</skillArr>
			<avtiveSkillCdOverT>0</avtiveSkillCdOverT>
			<!-- 攻击数据 -->
			<hurtArr><hurt>
				<imgLabel>move</imgLabel>
				<hurtRatio>0</hurtRatio>
				<shakeValue>4</shakeValue>
				<attackType>direct</attackType>
				<grapRect>-50,-111,100,105</grapRect>
			</hurt></hurtArr>
		</body>	
	</father>
	<father type="zombie" cnName="子弹">
		<bullet cnName="烟花炮-母弹">
			<name>LastdayBig</name>
			<cnName>烟花炮-母弹</cnName><noMagneticB>1</noMagneticB><noBeClearB>1</noBeClearB>
			<hurtRatio>1</hurtRatio>
			<bulletSkillArr>LastdayBigDieEvent</bulletSkillArr>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>1.5</bulletLife><bulletWidth>30</bulletWidth><bulletAngle>180</bulletAngle><hitType>rect</hitType>
			<attackGap>0.4</attackGap><attackDelay>0.18</attackDelay><bulletNum>1</bulletNum><shootPoint>-222,-163</shootPoint>
			<penetrationGap>1000</penetrationGap>
			<boomD selfB="1" bodyB="1" radius="40" />
			<bulletSpeed>12</bulletSpeed><speedD a="-0.3" min="4" />
			<!--图像动画属性------------------------------------------------------------ -->
			<bulletImgUrl con="add" raNum="30">bullet/fireball</bulletImgUrl><flipX>1</flipX>
			<smokeImgUrl con="filter" raNum="30">bullet/fireballSmoke</smokeImgUrl>
			<hitImgUrl soundUrl="boomSound/microBoom2">boomEffect/boom2</hitImgUrl>
		</bullet>
					<bullet cnName="烟花炮-子弹">
						<name>LastdayBigChild</name>
						<cnName>烟花炮-子弹</cnName><noMagneticB>1</noMagneticB>
						<hurtRatio>1</hurtRatio>
						<!--基本属性------------------------------------------------------------ -->
						<bulletLife>2</bulletLife><lifeRandom>0.2</lifeRandom><bulletAngle>0</bulletAngle><bulletWidth>35</bulletWidth><hitType>rect</hitType>
						<bulletNum>20</bulletNum><shootAngle>110</shootAngle>
						<penetrationGap>1000</penetrationGap>
						<bulletSpeed>15</bulletSpeed><speedD a="-1" min="5" random="0.2" />
						<!--图像动画属性------------------------------------------------------------ -->
						<bulletImgUrl raNum="30">bullet/gaiaBullet</bulletImgUrl><flipX>1</flipX>
						<smokeImgUrl con="filter" raNum="30">bulletHitEffect/smoke_small</smokeImgUrl>
						<hitImgUrl soundUrl="boomSound/microBoom1" soundVolume="0.3">boomEffect/boom1</hitImgUrl>
					</bullet>
		<bullet cnName="跟踪导弹">
			<name>LastdayMissle</name>
			<cnName>跟踪导弹</cnName><noMagneticB>1</noMagneticB>
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletAngle>-161</bulletAngle><bulletLife>6</bulletLife><bulletWidth>30</bulletWidth><hitType>rect</hitType>
			<attackGap>0.3</attackGap>
			<attackDelay>0.12</attackDelay>
			<bulletNum>2</bulletNum>			
			<shootAngle>4</shootAngle>
			<shootPoint>35,-228</shootPoint>
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<followD value="0.6" delay="1" />
			<!--运动属性------------------------------------------------------------ -->	
			<bulletSpeed>3</bulletSpeed>
			<speedD max="10" min="3" a="5" random="0.2"/>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30">LastdayTank/bullet1</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom2"  soundVolume="0.3"  shake="3,0.4,13">boomEffect/boom2</hitImgUrl><!-- 子弹图像【必备】 -->
			<smokeImgUrl con="add" >LastdayTank/smoke1</smokeImgUrl><!-- 子弹尾烟效果（默认为空） -->
		</bullet>
		<bullet cnName="机关枪">
			<name>LastdayViking</name>
			<cnName>机关枪</cnName><noMagneticB>1</noMagneticB>
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>1.5</bulletLife><bulletWidth>20</bulletWidth><bulletAngle>-175</bulletAngle><hitType>rect</hitType>
			<attackGap>0.9</attackGap><attackDelay>0.27</attackDelay><bulletNum>5</bulletNum><shootAngle>40</shootAngle><shakeAngle>1</shakeAngle>
			<shootNum>5</shootNum><shootGap>0.13</shootGap>
			<shootPoint>-209,-91</shootPoint>
			<penetrationGap>1000</penetrationGap>
			<bulletSpeed>13</bulletSpeed><speedD a="-0.3" min="4" />
			<!--图像动画属性------------------------------------------------------------ -->
			<bulletImgUrl con="filter" raNum="60">bullet/blueLaser</bulletImgUrl><flipX>1</flipX>
			<hitImgUrl soundUrl="sound/hand_hit">bulletHitEffect/fitHit</hitImgUrl>
		</bullet>
		<bullet cnName="三连炮">
			<name>LastdayBig3</name>
			<cnName>三连炮</cnName><noMagneticB>1</noMagneticB><noBeClearB>1</noBeClearB>
			<hurtRatio>1</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>4</bulletLife><bulletWidth>40</bulletWidth><hitType>rect</hitType>
			<attackGap>1.7</attackGap><attackDelay>0.5</attackDelay><bulletNum>1</bulletNum>
			<shootNum>3</shootNum><shootGap>0.43</shootGap>
			<positionD>
				<point shootPoint="-225,-115" bulletAngle="163" />
				<point shootPoint="-224,-139" bulletAngle="170" />
				<point shootPoint="-225,-164" bulletAngle="180" />
			</positionD>
			<penetrationGap>1000</penetrationGap>
			<bulletSpeed>13</bulletSpeed><speedD a="-0.3" min="4" />
			<!--图像动画属性------------------------------------------------------------ -->
			<bulletImgUrl raNum="30" con="filter">bullet/purpleFire</bulletImgUrl>
			<hitImgUrl soundUrl="boomSound/midBoom1"  shake="2,0.2,10">boomEffect/boom3</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
	</father>
	
	<father cn="技能" name="enemy">
		<skill>
			<name>LastdayAttackDefend</name><noCopyB>1</noCopyB>
			<cnName>元素部件</cnName><wantDescripB>1</wantDescripB>
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>0.1</duration>
			<description>坦克的部件，包括大炮(生化敏感)、机关枪(冷冻敏感)、导弹发射器(电磁敏感)、后轮胎(火焰敏感)。这些部件被打破之后，坦克将防御力大幅下降到只剩下10%。</description>
		</skill>
		<skill>
			<name>LastdayAir</name>
			<cnName>空中压制</cnName><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<ignoreSilenceB>1</ignoreSilenceB><ignoreImmunityB>1</ignoreImmunityB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<otherConditionArr>lifePerLess30</otherConditionArr>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>airMoveSpeed</effectType>
			<value>0</value>
			<mul>0.4</mul>
			<duration>0.5</duration>
			<range>99999</range>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="2foot">skillEffect/disabled_enemy</stateEffectImg>
			<description>生命值低于30%时，降低敌人在空中的移动速度。</description>
		</skill>
		<skill>
			<name>LastdaySpellImmunity</name>
			<cnName>免疫护罩</cnName><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cd>15</cd>
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<addType>state</addType>
			<effectType>spellImmunityB</effectType>
			<duration>10</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="body" con="add" soundUrl="LastdayTank/shield1">LastdayTank/spellImmunityShield</stateEffectImg>
			<description>释放免疫护罩，使自己对敌人的技能效果免疫，持续[duration]秒。</description>
		</skill>
		<skill>
			<name>LastdayHit</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>破壳</cnName><noSkillDodgeB>1</noSkillDodgeB>
			<ignoreNoSkillB>1</ignoreNoSkillB><noBeClearB>1</noBeClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>attackGapMul</effectType>
			<effectProArr>0.2</effectProArr>
			<mul>0.1</mul>
			<duration>5</duration>
			<stateEffectImg partType="shootPoint,hand_left,hand_right" raNum="25" followPartRaB="1">bulletHitEffect/smoke_black</stateEffectImg>
			<description>击中敌人有几率使其枪支失效，持续[duration]秒。</description>
		</skill>
		
		
					<skill>
						<name>LastdayPartUnder</name>
						<cnName>部件受伤</cnName>
						<noBeClearB>1</noBeClearB><ignoreNoSkillB>1</ignoreNoSkillB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>underHit</condition>
						<target>me</target>
						<!--效果------------------------------------------------------------ -->
						<addType>instantAndState</addType><noReStateB>1</noReStateB>
						<effectType>LastdayPartUnder</effectType>
						<mul>4</mul>
						<duration>0.1</duration>
						<description>各个部件对元素敏感不同；受到伤害使部件变红</description>
					</skill>
					<skill>
						<name>LastdayPartDie</name>
						<cnName>部件死亡</cnName>
						<noBeClearB>1</noBeClearB><ignoreNoSkillB>1</ignoreNoSkillB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>die</condition>
						<target>me</target>
						<!--效果------------------------------------------------------------ -->
						<addType>instant</addType>
						<effectType>LastdayPartDie</effectType>
					</skill>
	</father>
</data>