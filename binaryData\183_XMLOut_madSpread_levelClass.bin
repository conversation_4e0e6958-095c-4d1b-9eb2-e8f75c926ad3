<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="normal">
		<gather name="缺陷">
			<level name="madDefect">
				<!-- 关卡数据 -->
				<info noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<drop noB="1"/>
				<!-- 基本属性 -->
				<sceneLabel>HospitalUnder</sceneLabel>
				<!-- 发兵集 -->
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<!-- 人类 -->
					<unitOrder id="we1"  camp="we">
						<unit cnName="战争狂人" dieGotoState="stru"/>
						<unit cnName="亚瑟"/>
						<unit cnName="奇皇博士"/>
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>createUnit:we1; r_birth</order>
							<order>heroEverParasitic:战争狂人</order>
							<order>madDefect</order>
						</event>
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>madDefectKill</order><![CDATA[狂人受伤倒地]]>
						</event>
						<event><condition delay="0.3"></condition><order>hideSightCover</order></event>
						<event><condition delay="1.5"></condition><order>say; startList:s3</order></event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>say; startList:s4</order>
						</event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>say; startList:s5</order>
						</event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; win</order>
						</event>
					</group>
				</eventG>
			</level>	
		</gather>
		<gather name="飞行">
			<level name="madFly1">
				<!-- 关卡数据 -->
				<info diy="madFly" music="music_face/fly" preBulletArr="extremeGemDrop,magnetBullet,blackHoleMad,hurtBulletMad"  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<!-- 基本属性 -->
				<sceneLabel>HanGuangSub</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="we1" camp="we">
						<unit cnName="判决者" num="1" lifeMul="1" dpsMul="1" />
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><!-- 主角限制 -->
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:判决者</order><!-- 添加僵尸和巡逻 -->
						</event>
						<event>
							<condition delay="1"></condition>
							<order>level; diyEvent:start</order>
						</event>
						<event>
							<condition delay="0.01">task:state; madFly1:complete</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; 判决者</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="madFly2">
				<!-- 关卡数据 -->
				<info diy="madFly" music="music_face/fly" preBulletArr="extremeGemDrop,magnetBullet,blackHoleMad,hurtBulletMad"  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<!-- 基本属性 -->
				<sceneLabel>HanGuangSub</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="we1" camp="we">
						<unit cnName="判决者" num="1" lifeMul="1" dpsMul="1" />
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><!-- 主角限制 -->
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:判决者</order><!-- 添加僵尸和巡逻 -->
						</event>
						<event>
							<condition delay="1"></condition>
							<order>level; diyEvent:start</order>
						</event>
						<event>
							<condition delay="0.01">task:state; madFly2:complete</condition>
							<order>say; startList:s1</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; 判决者</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="madFly3">
				<!-- 关卡数据 -->
				<info diy="madFly" music="music_face/fly" preBulletArr="extremeGemDrop,magnetBullet,blackHoleMad,hurtBulletMad"  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<!-- 基本属性 -->
				<sceneLabel>HanGuangSub</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="we1" camp="we">
						<unit cnName="判决者" num="1" lifeMul="1" dpsMul="1" />
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><!-- 主角限制 -->
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:判决者</order><!-- 添加僵尸和巡逻 -->
						</event>
						<event>
							<condition delay="1"></condition>
							<order>level; diyEvent:start</order>
						</event>
						<event>
							<condition delay="0.01">task:state; madFly3:complete</condition>
							<order>say; startList:s1</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; 判决者</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="madFly4">
				<!-- 关卡数据 -->
				<info enemyLv="99" diy="madFly" music="music_face/fly" preBulletArr="extremeGemDrop,magnetBullet,blackHoleMad,hurtBulletMad"  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<drop noB="1"/>
				<!-- 基本属性 -->
				<sceneLabel>HanGuangSub</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal" noSuperB="1"></allDefault>
					<unitOrder id="we1" camp="we">
						<unit cnName="判决者"  lifeMul="0.07" dpsMul="400" skillArr="crazy_vehicle_8,groupLightMadFly" />
					</unitOrder>
					<unitOrder id="enemy0">
						<numberType>pro</numberType>
						<unit cnName="丛林特种兵" num="2" dpsMul="1" lifeMul="0.7" armsRange="rocketMammoth" />
						<unit cnName="雇佣兵" num="2" dpsMul="1" lifeMul="0.7" armsRange="shotgun1" />
					</unitOrder>
					<unitOrder id="enemy1">
						<numberType>pro</numberType>
						<unit cnName="雄鹰" num="1" lifeMul="2"/>
						<unit cnName="潜行者" num="1" lifeMul="2"/>
					</unitOrder>
					<unitOrder id="super1">
						<numberType>num</numberType>
						<unit cnName="大鹏" lifeMul="1" unitType="super" skillArr="slowMove_enemy,groupSpeedUp_enemy" />
					</unitOrder>
					<unitOrder id="super2">
						<numberType>num</numberType>
						<unit cnName="潜影者" lifeMul="1" unitType="super" skillArr="crazy_vehicle_5,groupLight_enemy" />
					</unitOrder>
					<unitOrder id="boss1">
						<numberType>num</numberType>
						<unit cnName="霸空雕" unitType="boss" lifeMul="1" dpsMul="0.7" skillArr="hiding_enemy" />
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><!-- 主角限制 -->
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:判决者</order><!-- 添加僵尸和巡逻 -->
						</event>
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy0; r1</order>
							<order>createUnit:enemy0; r2</order>
							<order>createUnit:enemy0; r3</order>
						</event>
						<event id="e2_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy0; r1</order>
							<order>createUnit:enemy0; r2</order>
							<order>createUnit:enemy0; r3</order>
						</event>
						<event id="e2_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:super1; r2</order>
						</event>
						<event id="e2_1">
							<condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:enemy0; r1</order>
							<order>createUnit:enemy0; r2</order>
							<order>createUnit:enemy0; r3</order>
						</event>
						<event id="e2_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition>
							<order>createUnit:enemy1; r2</order>
						</event>
						<event id="e2_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:super2; r2</order>
						</event>
						<event id="e2_1">
							<condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_1</condition>
							<order>createUnit:boss1; r2</order>
						</event>
						<event>
							<condition delay="0.5">enemyNumber:less_1</condition>
							<order>task:now; complete</order>
							<order>say; startList:s1</order>
						</event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; 判决者</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			
			<level name="madFlyBug">
				<!-- 关卡数据 -->
				<info diy="madFly" music="music_face/fly" preBulletArr="extremeGemDrop,magnetBullet,blackHoleMad,hurtBulletMad"  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<!-- 基本属性 -->
				<sceneLabel>HanGuangSub</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="we1" camp="we">
						<unit cnName="判决者" num="1" lifeMul="1" dpsMul="1" />
					</unitOrder>
				</unitG>
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order><!-- 主角限制 -->
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:判决者</order><!-- 添加僵尸和巡逻 -->
						</event>
						<event>
							<condition delay="1"></condition>
							<order>level; diyEvent:start</order>
						</event>
						<event>
							<condition delay="0.01">task:state; madFly1:complete</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; 判决者</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
		</gather>
		
		<gather name="战神测试">
			<level name="madBossShow">
				<!-- 发兵集************************************************ -->
				<info enemyLv="99"  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<drop noB="1"/>
				<sceneLabel>Hospital5</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG><allDefault aiOrder="patrolGlobal"></allDefault>
				<!-- 人类 -->
					<unitOrder id="we1"  camp="we">
						<unit cnName="战神" lifeMul="0.3" dpsMul="400" armsRange="extremeRocket" skillArr="crazy_pet_9,madArmsAttack,sumBossAtten,strong_enemy" />
					</unitOrder>
					
					<unitOrder id="enemy1">
						<numberType>number</numberType>
						<unit cnName="水管僵尸" num="2"/>
						<unit cnName="制图尸" num="3"/>
						<unit cnName="窃听者" num="3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>number</numberType>
						<unit cnName="水管僵尸" num="6"/>
						<unit cnName="制图尸" num="3"/>
						<unit cnName="窃听者" num="3"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="水管僵尸" num="6"/>
						<unit cnName="制图尸" num="4"/>
						<unit cnName="窃听者" num="4"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<numberType>number</numberType>
						<unit cnName="水管僵尸" num="6"/>
						<unit cnName="制图尸" num="4"/>
						<unit cnName="窃听者" num="1" unitType="boss" dpsMul="1.3" />
					</unitOrder>
				</unitG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order>
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:战神</order>
						</event>
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event id="e2_1"><condition delay="1">say:listOver</condition></event>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order></event> 
						<event id="e2_11">
							<condition>enemyNumber:less_1</condition>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; 战神</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="madBossPhone">
				<!-- 发兵集************************************************ -->
				<info enemyLv="99" preSkillArr="madmanHead" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<drop noB="1"/>
				<sceneLabel>Hospital1</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG><allDefault aiOrder="patrolGlobal"></allDefault>
				<!-- 人类 -->
					<unitOrder id="we1"  camp="we">
						<unit cnName="战神" lifeMul="0.4" dpsMul="400" skillArr="crazy_pet_9,madArmsAttack,alloyShell_8,sumBossAtten,strong_enemy,desertedHalo_enemy,madCloseLightning,findHide,toLand,summonShortLife,rigidBody_enemy" />
					</unitOrder>
					<unitOrder id="we2"  camp="we">
						<unit cnName="战争狂人"/>
					</unitOrder>
					
					<unitOrder id="enemy1">
						<numberType>number</numberType>
						<unit cnName="独眼僵尸" num="2"/>
						<unit cnName="伏地尸" num="3"/>
						<unit cnName="窃听者" num="3"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>number</numberType>
						<unit cnName="独眼僵尸" num="6"/>
						<unit cnName="伏地尸" num="3"/>
						<unit cnName="窃听者" num="3"/>
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="独眼僵尸" num="6"/>
						<unit cnName="伏地尸" num="4"/>
						<unit cnName="窃听者" num="4"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<numberType>number</numberType>
						<unit cnName="看门狗" unitType="boss" lifeMul="1.6" dpsMul="1"/>
					</unitOrder>
				</unitG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order>
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:战神</order>
						</event>
						<event><condition delay="0.5"></condition><order>say; startList:s1</order></event>
						<event id="e2_1"><condition delay="1">say:listOver</condition></event>
						<event id="e2_1"><condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="4" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order></event> 
						<event id="e2_11">
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:we2; r_birth</order>
							<order>madBossPhone; addMad</order>
							<order>heroEverParasitic:战争狂人</order>
						</event>
						<event><condition delay="1"></condition><order>say; startList:s2</order></event>
						<![CDATA[获得通讯器]]>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>madBossPhone; getPhone</order>
						</event>
						<event><condition delay="1"></condition><order>say; startList:s3</order></event>
						<![CDATA[变脸主角]]>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>madBossPhone; changeFace</order>
						</event>
						<event><condition delay="2"></condition><order>say; startList:sPartner</order></event>
						<![CDATA[脸变回来]]>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>madBossPhone; backFace</order>
						</event>
						<event><condition delay="1"></condition><order>say; startList:s4</order></event>
						<event>
							<condition delay="1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail"><condition delay="1">bodyEvent:die; 战神</condition><order>alert:yes; 任务失败！</order></event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
		</gather>
		<gather name="狂人篇-主角">
			<level name="madWarriorSec">
				<!-- 关卡数据 -->
				<info enemyLv="99" preSkillArr="madmanHead" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" mustSingleB="1" noDeviceB="1" />
				<drop noB="1" />
				<!-- 基本属性 -->
				<sceneLabel>BaiSha</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG>
					<allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="we1"  camp="we">
						<unit cnName="本我" armsRange="meltFlamer" lifeMul="99" dpsMul="500" skillArr="crazy_enemy,imploding_enemy" />
					</unitOrder>
					
					<unitOrder id="enemy1">
						<numberType>number</numberType>
						<unit cnName="狂人机器X" unitType="boss" lifeMul="0.3" dpsMul="0.00001"/>
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">1494,886,212,92 </rect>
					<rect id="r_over">3386,1084,48,100</rect>
					<rect id="r1">30,754,280,120</rect>
					<rect id="r2">1327,318,280,120</rect>
					<rect id="r3">3060,713,280,120</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">2156,638  ,118,64</rect>
					<rect label="addCharger">813,820,118,64</rect>
				</rectG>
				
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order>
							<order>createUnit:we1; r_birth</order><order>heroEverParasitic:本我</order>
							<order>madWarriorSec; add</order>
						</event>
						<event><condition delay="1"></condition><order>say; startList:s1</order></event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>madWarriorSec; face</order>
						</event>
						<event><condition delay="1"></condition><order>say; startList:s2</order></event>
						<event>
							<condition delay="0.5">say:listOver</condition>
							<order>createUnit:enemy1; r3</order>
						</event>
						<event id="e2_11">
							<condition delay="0.5">enemyNumber:less_1</condition>
							<order>say; startList:s3</order>
						</event>	
						<event>
							<condition delay="2">say:listOver</condition>
							<order>say; startList:s4</order>
						</event>
						<event id="e2_11">
							<condition>say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
	</father>
</data>