<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="new" cnName="新的">
		<head name="anniver10" cnName="十年相伴" diff="50">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsAll':0.20,'gemDropPro':0.16}</addObjJson>
		</head>
		<head name="armsSkinCreator" cnName="武器制造大师" diff="100">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'moreDpsMul':0.05,'moreLifeMul':0.10}</addObjJson>
		</head>
		
		<head name="bbs23" cnName="群组达人" diff="50" life="15">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'blackEquipDropPro':0.60}</addObjJson>
		</head>
		<head name="anniver9" cnName="九周年快乐" diff="50">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsAll':0.15,'gemDropPro':0.16}</addObjJson>
		</head>
		<head name="achieveKing" cnName="成就之皇" diff="160">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'lottery':15}</addObjJson>
		</head>
		<head name="achieveGod" cnName="成就之神" diff="180">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'lottery':19}</addObjJson>
		</head>
		<head name="anniver8" cnName="八周年快乐" diff="50">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsAll':0.12,'gemDropPro':0.15}</addObjJson>
		</head>
		<head name="zodiac12" cnName="十二生肖" diff="150">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'zodiacArmsHurtAdd':0.25}</addObjJson>
		</head>
		
		
		<head name="battle4" cnName="霞光领主" diff="160" life="7" iconUrl48="IconGather/battle4_48">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'lottery':10,'specialPartsDropPro':0.30}</addObjJson>
		</head>
		<head name="battle3" cnName="霞光天军" diff="100" life="7" iconUrl48="IconGather/battle3_48">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'lottery':5,'specialPartsDropPro':0.20}</addObjJson>
		</head>
		<head name="battle2" cnName="霞光雄狮" diff="80" life="7" iconUrl48="IconGather/battle2_48">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'lottery':4,'specialPartsDropPro':0.16}</addObjJson>
		</head>
		<head name="battle1" cnName="霞光劲旅" diff="60" life="7" iconUrl48="IconGather/battle1_48">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'lottery':3,'specialPartsDropPro':0.12}</addObjJson>
		</head>
		
		
		<head name="joyousFool" cnName="愚人欢乐" diff="50" life="365">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'lifeAll':20.24,'dpsAll':-20.24}</addObjJson>
		</head>
		
		<head name="anniver7" cnName="爆枪七周年" diff="50">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsAll':0.12,'gemDropPro':0.13}</addObjJson>
		</head>
		
		<head name="singleAF" cnName="单身狗" diff="40"  life="7">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'vehicleDpsMul':3.0,'vehicleDefMul':3.0}</addObjJson>
		</head>
		
		<head name="happyFool" cnName="愚人快乐" diff="40">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'lifeAll':0.70,'dpsAll':-0.40}</addObjJson>
		</head>
		
		
		
		<head name="anniver6" cnName="爆枪六周年" diff="50">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsAll':0.1,'gemDropPro':0.11}</addObjJson>
		</head>
		
		<head name="childrenDay" cnName="六一快乐" diff="30" iconUrl48="IconGather/childrenDay_48">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'maxJumpNumAdd':2}</addObjJson>
		</head>
		<head name="childrenFly" cnName="六一起飞" diff="30" life="365">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'maxJumpNumAdd':9999,'fgNE':0.3}</addObjJson>
		</head>
		
		<head name="anniver5" cnName="爆枪五周年" diff="50">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'lifeAll':0.2,'gemDropPro':0.1}</addObjJson>
		</head>
		<head name="anniver4" cnName="爆枪四周年" diff="50">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'lottery':10}</addObjJson>
		</head>
		
		<head name="anniver3" cnName="爆枪三周年" diff="50">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'blackEquipDropPro':0.40}</addObjJson>
		</head>
		
		
		<head name="qixi" cnName="七夕眷侣" diff="50"  life="30">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsAll':0.06,'loveAdd':50}</addObjJson>
		</head>
		
		<head name="zongzi" cnName="粽叶飘香" diff="40">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'lottery':2,'dpsAll':0.02}</addObjJson>
		</head>
		<head name="happySpring" cnName="春节快乐" diff="20">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'lottery':2}</addObjJson>
		</head>
		
		
		
		<head name="widerAll" cnName="秘境征服者" diff="80">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsAll':0.06,'lottery':5}</addObjJson>
		</head>
		
		
		<head name="superHero" cnName="超能英雄" diff="120"  life="15" iconUrl48="IconGather/superHero_48">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsMul':1.0,'rareArmsDropPro':0.70}</addObjJson>
		</head>
		<head name="specialSoldiers" cnName="特种奇兵" diff="120" life="15" iconUrl48="IconGather/specialSoldiers_48">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'lifeMul':1.0,'rareEquipDropPro':0.70}</addObjJson>
		</head>
		
		<head name="headshot_20" cnName="爆头王" diff="20">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsMul_sniper':0.02}</addObjJson>
		</head>
		<head name="goddiff_30" cnName="踏平霞光" diff="30">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'arenaStampDropNum':3}</addObjJson>
		</head>
		<head name="death" cnName="死神" diff="15">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'lifeRateMul':0.05}</addObjJson>
		</head>
		<head name="rareArms_5" cnName="武器收藏家" diff="20">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'rareArmsDropPro':0.06}</addObjJson>
		</head>
		<head name="fakeFinale" cnName="假的大结局" diff="150">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'lottery':5}</addObjJson>
		</head>
	</father>
	
	
	<father name="top" cnName="排行榜">
		<head name="dpsTop_1" cnName="战神" diff="150" life="9999" unlockLv="1">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'rareArmsDropPro':0.25}</addObjJson>
		</head>
		<head name="arena_1" cnName="竞技之王" diff="100" life="9999" unlockLv="1">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'arenaStampDropNum':8}</addObjJson>
		</head>
		<head name="rifle_1" cnName="步枪之王" diff="90" life="9999" unlockLv="1">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsMul_rifle':0.04}</addObjJson>
		</head>
		<head name="sniper_1" cnName="狙击之王" diff="90" life="9999" unlockLv="1">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsMul_sniper':0.04}</addObjJson>
		</head>
		<head name="shotgun_1" cnName="散弹之王" diff="90" life="9999" unlockLv="1">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsMul_shotgun':0.04}</addObjJson>
		</head>
		<head name="pistol_1" cnName="手枪之王" diff="90" life="9999" unlockLv="1">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsMul_pistol':0.04}</addObjJson>
		</head>
		<head name="rocket_1" cnName="火炮之王" diff="90" life="9999" unlockLv="1">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsMul_rocket':0.04}</addObjJson>
		</head>
		<head name="almighty_10" cnName="十项全能" diff="80" life="9999"  unlockLv="1">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsAll':0.10}</addObjJson>
		</head>
	</father>
	
	<father name="collection" cnName="收集">
		<head name="weapon_4" cnName="兵器大亨" diff="80">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'bloodStoneDropPro':0.15}</addObjJson>
		</head>
		<head name="device_4" cnName="装置大亨" diff="40">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'lifeCatalystDropPro':0.20}</addObjJson>
		</head>
		
		<head name="achieve_70" cnName="成就大亨" diff="50">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'lifeMul':0.10}</addObjJson>
		</head>
		<head name="achieve_123" cnName="成就之王" diff="70">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dodge':0.03}</addObjJson>
		</head>
		<head name="dominating" cnName="独霸一方" diff="130">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'critPro3':0.03}</addObjJson>
		</head>
	</father>
	
	<father name="union" cnName="军队">
		<head name="unionIsMyHome" cnName="公会是我家" diff="85">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsMul':0.05}</addObjJson>
		</head>
		
	</father>
	<father name="other" cnName="其他">
		<head name="wantUpgrade" cnName="我要升级" diff="20">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'expMul':0.25}</addObjJson>
		</head>
		<head name="petEvo_4" cnName="闲着蛋疼" diff="50">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'godStoneDropPro':0.15}</addObjJson>
		</head>
		<head name="vehicleEvo_4" cnName="载具新时代" diff="70">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'converStoneDropPro':0.15}</addObjJson>
		</head>
		<head name="ask_5" cnName="学霸" diff="70" life="60">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'weaponDropPro':0.20}</addObjJson>
		</head>
		<head name="heroSkill_21" cnName="全能人" diff="25">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'cdMul':0.03}</addObjJson>
		</head>
		
		<head name="armsRemake100" cnName="武器锻造家" diff="50" unlockLv="1">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'rareArmsDropPro':0.13}</addObjJson>
		</head>
		<head name="equipRemake100" cnName="装备锻造家" diff="50" unlockLv="1">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'rareEquipDropPro':0.13}</addObjJson>
		</head>
		
		
		<head name="baoqiang" cnName="爆枪突击" diff="50">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'blackArmsDropPro':0.20}</addObjJson>
		</head>
		<head name="gameKing" cnName="佣兵之王" diff="100">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsAll':0.08,'lifeAll':0.08}</addObjJson>
		</head>
		<head name="gameSuper" cnName="佣兵精英" diff="60">
			<condition fun="num" must="0" pro="getAchieveNum" info="获得[must]个。"  progressInfo="[compare]个"/>
			<addObjJson>{'dpsAll':0.06,'lifeAll':0.06}</addObjJson>
		</head>
	</father>
</data>