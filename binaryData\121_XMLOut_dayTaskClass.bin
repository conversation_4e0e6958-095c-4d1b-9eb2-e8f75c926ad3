<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="day" cnName="每日" dayNum="4" buyNum="6" clearCompleteAfterBuyNumB="1" tipText="每天第一次上线将清除前一天的任务进度。" autoUnlockByLevelB="1" maxLvLimitB="1">
		<task name="worldWar" cnName="僵尸世界大战" uiShowTime="999999" unlockLv="7"  moreKillEnemyNumIsMe="1">
			<shortText>万尸围城！前往[map]消灭它们！</shortText>
			<conditionText>僵尸 [nowNum]/[num]</conditionText>
			<uiConditionText>累计消灭 [nowNum]/[num] 个僵尸</uiConditionText>
			<description>模拟电影《僵尸世界大战》中群尸围城的壮观场景，利用手中的武器一同消灭它们！</description>
			<!-- 地图 -->
			<worldMapType>random</worldMapType>
			<fixedLevelUrl>dayTask/worldWar</fixedLevelUrl>
			<noEnemyWhenCompleteB>1</noEnemyWhenCompleteB>
			<!-- 刷新条件 -->
			<uiFleshCondition><one>bodyEvent:die; anyone</one></uiFleshCondition>
			<!-- 任务各等级 ------------------------------------------------------------ -->
			<growth>
				<task>
					<diff>0.3</diff>
					<condition type="collect" target="killEnemyNum" value="500"/>
					<gift>base;exp;1;;;;LevelEnemyExp</gift>
					<gift>base;coin;2;;;;LevelEnemyCoin</gift>
					<gift>things;equipBox;3</gift>
					<gift>things;equipHighBox;1</gift>
					<gift showLv="50">things;bloodStone;1</gift>
					<gift showLv="90">things;partsChest72;2</gift>
				</task>
				<task>
					<diff>1</diff>
					<condition type="collect" target="killEnemyNum" value="1000"/>
					<gift>base;exp;2;;;;LevelEnemyExp</gift>
					<gift>base;coin;4;;;;LevelEnemyCoin</gift>
					<gift>things;equipBox;6</gift>
					<gift>things;equipHighBox;2</gift>
					<gift showLv="50">things;bloodStone;2</gift>
					<gift showLv="90">things;partsChest72;5</gift>
				</task>
				<task>
					<diff>2.5</diff>
					<condition type="collect" target="killEnemyNum" value="1500"/>
					<gift>base;exp;3;;;;LevelEnemyExp</gift>
					<gift>base;coin;6;;;;LevelEnemyCoin</gift>
					<gift>things;equipBox;9</gift>
					<gift>things;equipHighBox;3</gift>
					<gift showLv="50">things;bloodStone;4</gift>
					<gift showLv="90">things;partsChest72;3</gift>
					<gift showLv="90">things;partsChest75;1</gift>
				</task>
				<task unlockLv="90">
					<diff>7</diff>
					<condition type="collect" target="killEnemyNum" value="1700"/>
					<gift>base;exp;4;;;;LevelEnemyExp</gift>
					<gift>base;coin;8;;;;LevelEnemyCoin</gift>
					<gift>things;equipHighBox;4</gift>
					<gift showLv="50">things;bloodStone;5</gift>
					<gift showLv="90">things;partsChest75;2</gift>
				</task>
				<task unlockLv="95">
					<diff>20</diff>
					<condition type="collect" target="killEnemyNum" value="2000"/>
					<gift>base;exp;5;;;;LevelEnemyExp</gift>
					<gift>base;coin;10;;;;LevelEnemyCoin</gift>
					<gift>things;equipHighBox;5</gift>
					<gift showLv="50">things;bloodStone;5</gift>
					<gift showLv="90">things;partsChest75;3</gift>
				</task>
			</growth>
		</task>
		<task name="bet" cnName="特种兵的赌约" uiShowTime="999999" unlockLv="8" moreKillEnemyNumIsMe="1">
			<shortText>前往[map]和特种兵比拼消灭敌人的数量！</shortText>
			<conditionText>macth_kills_Jungle</conditionText>
			<uiConditionText>在消灭所有僵尸之后，你灭敌数必须超过特种兵</uiConditionText>
			<description>与特种兵阿天比拼灭敌数，胜利后可以获得他收藏的武器箱。</description>
			<!-- 地图 -->
			<worldMapType>random</worldMapType>
			<fixedLevelUrl>dayTask/bet</fixedLevelUrl>
			<!-- 刷新条件 -->
			<uiFleshCondition><one>bodyEvent:die; anyone</one></uiFleshCondition>
			
			<!-- 任务各等级 ------------------------------------------------------------ -->
			<growth>
				<task>
					<diff>0.5</diff>
					<gift>base;exp;1;;;;LevelEnemyExp</gift>
					<gift>base;coin;2;;;;LevelEnemyCoin</gift>
					<gift>things;armsBox;2</gift>
					<gift>things;armsHighBox;1</gift>
					<gift showLv="90">things;partsChest72;2</gift>
				</task>
				<task>
					<diff>1.2</diff>
					<gift>base;exp;2;;;;LevelEnemyExp</gift>
					<gift>base;coin;4;;;;LevelEnemyCoin</gift>
					<gift>things;armsBox;4</gift>
					<gift>things;armsHighBox;2</gift>
					<gift showLv="70">things;weaponChest;1</gift>
					<gift showLv="90">things;partsChest72;5</gift>
				</task>
				<task>
					<diff>4</diff>
					<gift>base;exp;3;;;;LevelEnemyExp</gift>
					<gift>base;coin;6;;;;LevelEnemyCoin</gift>
					<gift>things;armsBox;6</gift>
					<gift>things;armsHighBox;3</gift>
					<gift showLv="70">things;weaponChest;2</gift>
					<gift showLv="90">things;partsChest72;3</gift>
					<gift showLv="90">things;partsChest75;1</gift>
				</task>
				<task unlockLv="90">
					<diff>10</diff>
					<gift>base;exp;4;;;;LevelEnemyExp</gift>
					<gift>base;coin;8;;;;LevelEnemyCoin</gift>
					<gift>things;armsHighBox;4</gift>
					<gift showLv="70">things;weaponChest;3</gift>
					<gift showLv="90">things;partsChest75;2</gift>
				</task>
				<task unlockLv="95">
					<diff>25</diff>
					<gift>base;exp;5;;;;LevelEnemyExp</gift>
					<gift>base;coin;10;;;;LevelEnemyCoin</gift>
					<gift>things;armsHighBox;5</gift>
					<gift showLv="70">things;weaponChest;3</gift>
					<gift showLv="90">things;partsChest75;3</gift>
				</task>
			</growth>
		</task>
		<task name="oldRocket" cnName="古老的火炮" uiShowTime="999999" unlockLv="9">
			<limit vehicleB="0" />
			<shortText>前往[map]用古老火炮对僵尸进行爆头！</shortText>
			<conditionText>僵尸 [nowNum]/[num]</conditionText>
			<uiConditionText>在关卡中爆头[num]只僵尸，炮弹限制100枚</uiConditionText>
			<description>在来到沃土镇之前，你手里只有一把祖传的古老火炮，这把火炮发射推力很弱，对僵尸的身体几乎造成不了什么伤害，但对僵尸的头部伤害极大。</description>
			<!-- 地图 -->
			<worldMapType>random</worldMapType>
			<fixedLevelUrl>dayTask/oldRocket</fixedLevelUrl>
			<noEnemyWhenCompleteB>1</noEnemyWhenCompleteB>
			<!-- 任务各等级 ------------------------------------------------------------ -->
			<growth>
				<task>
					<diff>0.5</diff>
					<condition type="collect" target="killEnemyNum" value="15" cumulativeType="no" />
					<gift>base;exp;1;;;;LevelEnemyExp</gift>
					<gift>base;coin;2;;;;LevelEnemyCoin</gift>
					<gift>things;armsBox;2</gift>
					<gift>things;armsHighBox;1</gift>
					<gift>things;equipBox;2</gift>
					<gift>things;equipHighBox;1</gift>
					<gift showLv="70">things;deviceChest;1</gift>
					<gift showLv="90">things;partsChest72;2</gift>
				</task>
				<task>
					<diff>1.5</diff>
					<condition type="collect" target="killEnemyNum" value="35" cumulativeType="no"/>
					<gift>base;exp;2;;;;LevelEnemyExp</gift>
					<gift>base;coin;4;;;;LevelEnemyCoin</gift>
					<gift>things;armsBox;4</gift>
					<gift>things;armsHighBox;2</gift>
					<gift>things;equipBox;4</gift>
					<gift>things;equipHighBox;2</gift>
					<gift showLv="70">things;deviceChest;2</gift>
					<gift showLv="90">things;partsChest72;5</gift>
				</task>
				<task>
					<diff>4</diff>
					<condition type="collect" target="killEnemyNum" value="70" cumulativeType="no"/>
					<gift>base;exp;3;;;;LevelEnemyExp</gift>
					<gift>base;coin;6;;;;LevelEnemyCoin</gift>
					<gift>things;armsBox;6</gift>
					<gift>things;armsHighBox;3</gift>
					<gift>things;equipBox;6</gift>
					<gift>things;equipHighBox;3</gift>
					<gift showLv="70">things;deviceChest;3</gift>
					<gift showLv="90">things;partsChest72;3</gift>
					<gift showLv="90">things;partsChest75;1</gift>
				</task>
				<task unlockLv="90">
					<diff>10</diff>
					<condition type="collect" target="killEnemyNum" value="85" cumulativeType="no"/>
					<gift>base;exp;4;;;;LevelEnemyExp</gift>
					<gift>base;coin;8;;;;LevelEnemyCoin</gift>
					<gift>things;armsHighBox;4</gift>
					<gift>things;equipHighBox;4</gift>
					<gift showLv="70">things;deviceChest;4</gift>
					<gift showLv="90">things;partsChest75;2</gift>
				</task>
				<task unlockLv="95">
					<diff>25</diff>
					<condition type="collect" target="killEnemyNum" value="95" cumulativeType="no"/>
					<gift>base;exp;5;;;;LevelEnemyExp</gift>
					<gift>base;coin;10;;;;LevelEnemyCoin</gift>
					<gift>things;armsHighBox;5</gift>
					<gift>things;equipHighBox;5</gift>
					<gift showLv="70">things;deviceChest;4</gift>
					<gift showLv="90">things;partsChest75;3</gift>
				</task>
			</growth>
		</task>
		<task name="runAway" cnName="逃出生天" uiShowTime="999999" unlockLv="10">
			<limit vehicleB="0" />
			<shortText>在[map]躲过熔岩群袭击，你要存活下来！</shortText>
			<conditionText>坚持 [time]</conditionText>
			<uiConditionText>坚持 [time]</uiConditionText>
			<description>在僵尸病毒入侵之前，霞光市的西峰火山发生了一次大爆发，产生的熔岩石群像导弹一般席卷整个大陆，你要逃过这次熔岩灾难，存活下来！</description>
			<!-- 地图 -->
			<worldMapType>random</worldMapType>
			<fixedLevelUrl>dayTask/runAway</fixedLevelUrl>
			<noEnemyWhenCompleteB>1</noEnemyWhenCompleteB>
			<!-- 任务各等级 ------------------------------------------------------------ -->
			<growth>
				<task>
					<condition time="60" timeType="win" />
					<gift>base;exp;1;;;;LevelEnemyExp</gift>
					<gift>base;coin;2;;;;LevelEnemyCoin</gift>
					<gift>things;armsBox;2</gift>
					<gift>things;armsHighBox;1</gift>
					<gift>things;equipBox;2</gift>
					<gift>things;equipHighBox;1</gift>
					<gift showLv="76">things;keyChest;1</gift>
					<gift showLv="90">things;partsChest72;2</gift>
				</task>
				<task>
					<condition time="100" timeType="win" />
					<gift>base;exp;2;;;;LevelEnemyExp</gift>
					<gift>base;coin;4;;;;LevelEnemyCoin</gift>
					<gift>things;armsBox;4</gift>
					<gift>things;armsHighBox;2</gift>
					<gift>things;equipBox;4</gift>
					<gift>things;equipHighBox;2</gift>
					<gift showLv="76">things;keyChest;2</gift>
					<gift showLv="90">things;partsChest72;5</gift>
				</task>
				<task>
					<condition time="100" timeType="win" />
					<gift>base;exp;3;;;;LevelEnemyExp</gift>
					<gift>base;coin;6;;;;LevelEnemyCoin</gift>
					<gift>things;armsBox;6</gift>
					<gift>things;armsHighBox;3</gift>
					<gift>things;equipBox;6</gift>
					<gift>things;equipHighBox;3</gift>
					<gift showLv="76">things;keyChest;3</gift>
					<gift showLv="90">things;partsChest72;3</gift>
					<gift showLv="90">things;partsChest75;1</gift>
				</task>
				<task unlockLv="90">
					<condition time="100" timeType="win" />
					<gift>base;exp;4;;;;LevelEnemyExp</gift>
					<gift>base;coin;8;;;;LevelEnemyCoin</gift>
					<gift>things;armsHighBox;4</gift>
					<gift>things;equipHighBox;4</gift>
					<gift showLv="76">things;keyChest;4</gift>
					<gift showLv="90">things;partsChest75;2</gift>
				</task>
				<task unlockLv="95">
					<condition time="100" timeType="win" />
					<gift>base;exp;5;;;;LevelEnemyExp</gift>
					<gift>base;coin;10;;;;LevelEnemyCoin</gift>
					<gift>things;armsHighBox;5</gift>
					<gift>things;equipHighBox;5</gift>
					<gift showLv="76">things;keyChest;4</gift>
					<gift showLv="90">things;partsChest75;3</gift>
				</task>
			</growth>
		</task>
		<task name="coinChase" cnName="银币追逐者" uiShowTime="999999" unlockLv="11">
			<shortText>前往[map]收集大银币。</shortText>
			<conditionText>剩余时间 [time][n]大银币 [nowNum]/[num]</conditionText>
			<uiConditionText>在时间 [time] 之内收集大银币 [num] 个</uiConditionText>
			<description>这是一项考验操作的任务。玩家需在规定时间内收集地图内指定数量的银币。</description>
			<!-- 地图 -->
			<worldMapType>random</worldMapType>
			<fixedLevelUrl>dayTask/coinChase</fixedLevelUrl>
			<noEnemyWhenCompleteB>1</noEnemyWhenCompleteB>
			<!-- 任务各等级 ------------------------------------------------------------ -->
			<growth>
				<task>
					<condition type="collect" target="hitDrop" targetId="addCoin_task" value="30" cumulativeType="no" time="130" timeType="fail"/>
					<gift>base;exp;1;;;;LevelEnemyExp</gift>
					<gift>base;coin;2;;;;LevelEnemyCoin</gift>
					<gift>things;armsBox;2</gift>
					<gift>things;armsHighBox;1</gift>
					<gift>things;equipBox;2</gift>
					<gift>things;equipHighBox;1</gift>
					<gift showLv="65">things;strengthenStone;1</gift>
					<gift showLv="90">things;partsChest72;1</gift>
				</task>
				<task>
					<condition type="collect" target="hitDrop" targetId="addCoin_task" value="50" cumulativeType="no" time="130" timeType="fail"/>
					<gift>base;exp;2;;;;LevelEnemyExp</gift>
					<gift>base;coin;4;;;;LevelEnemyCoin</gift>
					<gift>things;armsBox;4</gift>
					<gift>things;armsHighBox;2</gift>
					<gift>things;equipBox;4</gift>
					<gift>things;equipHighBox;2</gift>
					<gift showLv="65">things;strengthenStone;2</gift>
					<gift showLv="90">things;partsChest72;3</gift>
				</task>
				<task>
					<condition type="collect" target="hitDrop" targetId="addCoin_task" value="70" cumulativeType="no" time="130" timeType="fail"/>
					<gift>base;exp;3;;;;LevelEnemyExp</gift>
					<gift>base;coin;6;;;;LevelEnemyCoin</gift>
					<gift>things;armsBox;6</gift>
					<gift>things;armsHighBox;3</gift>
					<gift>things;equipBox;6</gift>
					<gift>things;equipHighBox;3</gift>
					<gift showLv="65">things;strengthenStone;3</gift>
					<gift showLv="90">things;partsChest72;5</gift>
				</task>
				<task unlockLv="90">
					<condition type="collect" target="hitDrop" targetId="addCoin_task" value="80" cumulativeType="no" time="130" timeType="fail"/>
					<gift>base;exp;4;;;;LevelEnemyExp</gift>
					<gift>base;coin;8;;;;LevelEnemyCoin</gift>
					<gift>things;armsHighBox;4</gift>
					<gift>things;equipHighBox;4</gift>
					<gift showLv="65">things;strengthenStone;4</gift>
					<gift showLv="90">things;partsChest75;2</gift>
				</task>
				<task unlockLv="95">
					<condition type="collect" target="hitDrop" targetId="addCoin_task" value="90" cumulativeType="no" time="130" timeType="fail"/>
					<gift>base;exp;5;;;;LevelEnemyExp</gift>
					<gift>base;coin;10;;;;LevelEnemyCoin</gift>
					<gift>things;armsHighBox;5</gift>
					<gift>things;equipHighBox;5</gift>
					<gift showLv="65">things;strengthenStone;4</gift>
					<gift showLv="90">things;partsChest75;3</gift>
				</task>
			</growth>
		</task>
		<task name="sniperKing" cnName="狙击之王" uiShowTime="999999" unlockLv="85">
			<limit vehicleB="0" propsB="0" skillB="0" />
			<shortText>前往[map]用狙击消灭怪物</shortText>
			<conditionText>怪物 [nowNum]/[num]</conditionText>
			<uiConditionText>累计消灭 [nowNum]/[num] 个怪物</uiConditionText>
			<description>任务中你将无法动弹，并且不能使用载具、装置、副手，只能用指定狙击枪。任务中将出现3种敌人，击中每种敌人的脆弱部位将直接瞬秒它，敌人脆弱部位如下：科研僵尸（头部）、水管僵尸（身体）、吸血蝙蝠（全身）。</description>
			<!-- 地图 -->
			<worldMapType>range</worldMapType>
			<worldMapId>FengWei,XiChi,BaiLu,ShuiSheng,ZhuTou,ShuangTa,BeiDou,DongShan,QingSha,QingMing,NanTang,YouLing,XiFeng,BaoLun,LuYu,BaWang,PingChuan,BaiSha,ZhongXin,HouJin,ShaJiang,PrisonDoor</worldMapId>
			<fixedLevelUrl>dayTask/sniperKing</fixedLevelUrl>
			<noEnemyWhenCompleteB>1</noEnemyWhenCompleteB>
			<!-- 任务各等级 ------------------------------------------------------------ -->
			<growth>
				<task>
					<diff>0.5</diff>
					<condition type="collect" target="killEnemyNum" value="30"/>
					<gift>things;allBlackEquipCash;4</gift>
					<gift showLv="90" tipB="1">things;electricGem;2</gift>
				</task>
				<task>
					<diff>0.7</diff>
					<condition type="collect" target="killEnemyNum" value="45"/>
					<gift>things;allBlackEquipCash;5</gift>
					<gift showLv="90" tipB="1">things;electricGem;3</gift>
				</task>
				<task>
					<diff>1</diff>
					<condition type="collect" target="killEnemyNum" value="60"/>
					<gift>things;allBlackEquipCash;6</gift>
					<gift showLv="90" tipB="1">things;electricGem;4</gift>
				</task>
				<task>
					<diff>1</diff>
					<condition type="collect" target="killEnemyNum" value="75"/>
					<gift>things;allBlackEquipCash;8</gift>
					<gift showLv="90" tipB="1">things;electricGem;6</gift>
				</task>
			</growth>
		</task>
		<task name="invincibleZombie" cnName="大逃亡" uiShowTime="999999" unlockLv="85">
			<limit vehicleB="0" propsB="0" skillB="0" />
			<shortText>在[map]躲避无敌僵尸的攻击，你要尽量活下来！</shortText>
			<conditionText>坚持 [time]</conditionText>
			<uiConditionText>坚持 [time]</uiConditionText>
			<description>一群无敌自爆僵尸在追击你，你要躲避它们的攻击，尽量活下来！注意，游戏中将随机出现无敌药水。</description>
			<!-- 地图 -->
			<worldMapType>random</worldMapType>
			<fixedLevelUrl>dayTask/invincibleZombie</fixedLevelUrl>
			<noEnemyWhenCompleteB>1</noEnemyWhenCompleteB>
			<!-- 任务各等级 ------------------------------------------------------------ -->
			<growth>
				<task>
					<diff>0.5</diff>
					<condition time="40" timeType="win" />
					<gift>things;allBlackCash;4</gift>
					<gift showLv="90" tipB="1">things;frozenGem;2</gift>
				</task>
				<task>
					<diff>0.7</diff>
					<condition time="55" timeType="win" />
					<gift>things;allBlackCash;5</gift>
					<gift showLv="90" tipB="1">things;frozenGem;3</gift>
				</task>
				<task>
					<diff>1</diff>
					<condition time="70" timeType="win" />
					<gift>things;allBlackCash;6</gift>
					<gift showLv="90" tipB="1">things;frozenGem;4</gift>
				</task>
				<task>
					<diff>1</diff>
					<condition time="85" timeType="win" />
					<gift>things;allBlackCash;8</gift>
					<gift showLv="90" tipB="1">things;frozenGem;6</gift>
				</task>
			</growth>
		</task>
		<task name="bulletRain" cnName="枪林弹雨" uiShowTime="999999" unlockLv="85">
			<limit vehicleB="0" propsB="0" skillB="0" />
			<shortText>在[map]的疯狂导弹攻击中活下来！</shortText>
			<conditionText>坚持 [time]</conditionText>
			<uiConditionText>坚持 [time]</uiConditionText>
			<description>你被困在实验室里，正面遭受大量的导弹攻击，你要利用弹跳躲避它们！碰到闪电球将清空当前所有导弹。同时，游戏中将随机出现无敌药水。</description>
			<!-- 地图 -->
			<worldMapId>PrisonDoor</worldMapId>
			<levelId>dayTask/bulletRain</levelId>
			<!-- 任务各等级 ------------------------------------------------------------ -->
			<growth>
				<task>
					<diff>0.5</diff>
					<condition time="40" timeType="win" />
					<gift>things;allBlackEquipCash;4</gift>
					<gift showLv="90" tipB="1">things;artifactChest;2</gift>
				</task>
				<task>
					<diff>0.7</diff>
					<condition time="55" timeType="win" />
					<gift>things;allBlackEquipCash;5</gift>
					<gift showLv="90" tipB="1">things;artifactChest;3</gift>
				</task>
				<task>
					<diff>1</diff>
					<condition time="70" timeType="win" />
					<gift>things;allBlackEquipCash;6</gift>
					<gift showLv="90" tipB="1">things;artifactChest;4</gift>
				</task>
				<task>
					<diff>1</diff>
					<condition time="85" timeType="win" />
					<gift>things;allBlackEquipCash;8</gift>
					<gift showLv="90" tipB="1">things;artifactChest;6</gift>
				</task>
			</growth>
		</task>
		<task name="flySky" cnName="直上云霄" uiShowTime="999999" unlockLv="85">
			<limit vehicleB="0" propsB="0" skillB="0" />
			<shortText>踩踏指定数量的蝙蝠</shortText>
			<conditionText>踩踏[nowNum]/[num]只蝙蝠</conditionText>
			<uiConditionText>踩踏[num]只蝙蝠</uiConditionText>
			<description>踩踏一只蝙蝠你将自动弹跳1次，利用弹跳踩到更多的蝙蝠。踩踏发着紫光的蝙蝠将使地面抬升至你脚下，并且消灭脚下所有的蝙蝠。</description>
			<!-- 地图 -->
			<worldMapId>FlySkyScene</worldMapId>
			<levelId>dayTask/flySky</levelId>
			<!-- 任务各等级 ------------------------------------------------------------ -->
			<growth>
				<task>
					<diff>0.5</diff>
					<condition type="collect" target="killEnemyNum" value="35" cumulativeType="no" targetId="flySky" />
					<gift>things;allBlackCash;4</gift>
					<gift showLv="90" tipB="1">things;artifactChest;2</gift>
				</task>
				<task>
					<diff>0.7</diff>
					<condition type="collect" target="killEnemyNum" value="50" cumulativeType="no" targetId="flySky" />
					<gift>things;allBlackCash;5</gift>
					<gift showLv="90" tipB="1">things;artifactChest;3</gift>
				</task>
				<task>
					<diff>1</diff>
					<condition type="collect" target="killEnemyNum" value="80" cumulativeType="no" targetId="flySky" />
					<gift>things;allBlackCash;6</gift>
					<gift showLv="90" tipB="1">things;artifactChest;4</gift>
				</task>
				<task>
					<diff>1</diff>
					<condition type="collect" target="killEnemyNum" value="150" cumulativeType="no" targetId="flySky" />
					<gift>things;allBlackCash;8</gift>
					<gift showLv="90" tipB="1">things;artifactChest;6</gift>
				</task>
			</growth>
		</task>
	</father>
</data>