<?xml version="1.0" encoding="utf-8" ?>
<data>
	<anniverEgg>
		<one name="anniverEgg" cnName="拆红包">
			<gift pro="70">base;anniCoin;6</gift>
			<gift pro="50">base;tenCoin;5</gift>
			<gift pro="2">things;exploitCard;1</gift>
			<gift pro="3">things;demonSpreadCard;1</gift>
			<gift pro="5">things;demBall;10</gift>
			<gift pro="5">things;demStone;5</gift>
			<gift pro="5">things;zodiacCash;5</gift>
			<gift pro="2">things;partsChest90;1</gift>
		</one>
	</anniverEgg>
	<anniverSign>
		<one name="anniverSign1" cnName="每日" mustLevel="1">
			<gift>base;anniCoin;2</gift>
			<gift>base;tenCoin;5</gift>
			<gift>things;allBlackCash;10</gift>
			<gift>things;allBlackEquipCash;10</gift>
		</one>
		<one name="anniverSign5" mustLevel="3">
			<gift>base;anniCoin;12</gift>
			<gift>base;tenCoin;24</gift>
			<gift>things;starTrails;2</gift>
			<gift>things;intensDrug;4</gift>
			<gift>things;allBlackCash;12</gift>
			<gift>things;allBlackEquipCash;12</gift>
		</one>
		<one name="anniverSign10" mustLevel="5">
			<gift>base;anniCoin;18</gift>
			<gift>base;tenCoin;36</gift>
			<gift>things;starTrails;3</gift>
			<gift>things;intensDrug;6</gift>
			<gift>things;allBlackCash;18</gift>
			<gift>things;allBlackEquipCash;18</gift>
		</one>
		<one name="anniverSign15" mustLevel="8">
			<gift>base;anniCoin;25</gift>
			<gift>base;tenCoin;50</gift>
			<gift>things;starTrails;5</gift>
			<gift>things;intensDrug;10</gift>
			<gift>things;allBlackCash;25</gift>
			<gift>things;allBlackEquipCash;25</gift>
		</one>
		<one name="anniverSign20" mustLevel="12">
			<gift>base;anniCoin;37</gift>
			<gift>base;tenCoin;74</gift>
			<gift>things;starTrails;7</gift>
			<gift>things;intensDrug;14</gift>
			<gift>things;allBlackCash;37</gift>
			<gift>things;allBlackEquipCash;37</gift>
		</one>
		<one name="anniverSign25" mustLevel="16">
			<gift>base;anniCoin;50</gift>
			<gift>base;tenCoin;100</gift>
			<gift>things;starTrails;9</gift>
			<gift>things;intensDrug;18</gift>
			<gift>things;allBlackCash;50</gift>
			<gift>things;allBlackEquipCash;50</gift>
		</one>
		<one name="anniverSign30" mustLevel="20">
			<gift>base;anniCoin;62</gift>
			<gift>base;tenCoin;124</gift>
			<gift>things;starTrails;11</gift>
			<gift>things;intensDrug;22</gift>
			<gift>things;allBlackCash;137</gift>
			<gift>things;allBlackEquipCash;137</gift>
		</one>
		<one name="anniverSign35" mustLevel="24">
			<gift>base;anniCoin;75</gift>
			<gift>base;tenCoin;150</gift>
			<gift>things;starTrails;23</gift>
			<gift>things;intensDrug;26</gift>
			<gift>bossCard;anniverSign10;1</gift>
			<gift>head;anniver10;1</gift>
		</one>
	</anniverSign>
	
	<anniverOther>
		
	</anniverOther>
</data>