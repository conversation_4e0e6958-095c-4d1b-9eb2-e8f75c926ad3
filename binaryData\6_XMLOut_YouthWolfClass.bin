<?xml version="1.0" encoding="utf-8" ?>
<data>
	
	<father name="wilder">
		
		
		<body name="阿巴达斯" shell="normal">
			
			<name>YouthWolf</name>
			<cnName>阿巴达斯</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/enemy/YouthWolf.swf</swfUrl>
			<!-- 基本系数 -->
			<rosRatio>1</rosRatio>
			<lifeRatio>1.8</lifeRatio>
			<showLevel>999</showLevel>
			<!-- 图像 -->
			<imgType>normal</imgType>
			<imgArr>
				stand,move,run
				,shootAttack,shakeAttack,comboAttack,obliqueAttack
				,hurt1,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<lifeBarExtraHeight>-40</lifeBarExtraHeight>
			<handAddRa>90</handAddRa>
			<!-- 碰撞体积 -->
			<hitRect>-18,-96,36,96</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>12</maxVx>
			<runStartVx>8</runStartVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel>YouthWolf_AIExtra</extraAIClassLabel>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr>YouthWolf_crazy,YouthWolf_combo,YouthWolf_oblique,YouthWolf_shake,KingRabbitTreater,cmldef_enemy,fightReduct2,defenceBounce_enemy</bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>shootAttack</imgLabel>
					<bulletLabel>YouthWolfShoot</bulletLabel>
					<grapRect>-800,-95,750,108</grapRect>
					<hurtMul>0.2</hurtMul>
					<shakeValue>4</shakeValue>
					<attackType>holy</attackType>
				</hurt>
				<hurt info="斜拳">
					<imgLabel>obliqueAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<grapRect>-400,-111,100,150</grapRect>
					<hurtMul>0.3</hurtMul>
					<shakeValue>4</shakeValue>
					<attackType>holy</attackType>
					<hitImgUrl soundUrl="sound/hand_hit">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt info="抱拳">
					<imgLabel>shakeAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<grapRect>-400,-111,100,150</grapRect>
					<skillArr>slowMove_GasBomb</skillArr>
					<hurtMul>0.6</hurtMul>
					<shakeValue>4</shakeValue>
					<attackType>holy</attackType>
					<hitImgUrl soundUrl="sound/vehicle_hit4">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt info="连拳">
					<imgLabel>comboAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<grapRect>-400,-111,100,150</grapRect>
					<hurtMul>0.3</hurtMul>
					<shakeValue>4</shakeValue>
					<attackType>holy</attackType>
					<hitImgUrl soundUrl="sound/hand_hit">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>	
	</father>
	<father name="enemy">
		<bullet cnName="地波">
			<name>YouthWolfShoot</name>
			<cnName>地波</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<bulletLife>10</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.7</attackGap>
			<attackDelay>0.4</attackDelay>
			<bulletAngle>180.01</bulletAngle>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-82,-17</shootPoint>
			<bulletSpeed>20</bulletSpeed>
			<penetrationGap>1000</penetrationGap>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1" con="add">YouthWolf/bulletRight</bulletImgUrl>
			<bulletLeftImgUrl raNum="1" con="add">YouthWolf/bulletLeft</bulletLeftImgUrl>
			<hitImgUrl con="add" soundUrl="sound/magicHit2">bulletHitEffect/fitHit</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
		
		
		<![CDATA[技能===============================================]]>
		<skill name="斜拳">
			<name>YouthWolf_oblique</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>斜拳</cnName>
			<cd>7</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>250</conditionRange>
			
			<target>me</target>
			<addType>state</addType>
			<effectType>invincible</effectType>
			<duration>1</duration>
			<meActionLabel>obliqueAttack</meActionLabel>
			<description>阿巴达斯从空中向敌人发起俯冲拳攻击。</description>
		</skill>
		
		<skill name="抱拳">
			<name>YouthWolf_shake</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>抱拳</cnName>
			<cd>9</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			
			<target>me</target>
			<addType>state</addType>
			<effectType>invincible</effectType>
			<duration>1</duration>
			<meActionLabel>shakeAttack</meActionLabel>
			<description>阿巴达斯腾空而起，对脚下的敌人发起抱拳震击，并减速敌人。</description>
			
		</skill>
		<skill name="连拳">
			<name>YouthWolf_combo</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>连拳</cnName>
			<cd>11</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			
			<target>me</target>
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>1.3</duration>
			<meActionLabel>comboAttack</meActionLabel>
			<pointEffectImg raNum="1" con="filter">YouthWolf/shadowRight</pointEffectImg>
			<otherEffectImg raNum="1" con="filter">YouthWolf/shadowLeft</otherEffectImg>
			
			<description>阿巴达斯闪烁到任意位置进行拳击，并有几率再次闪烁，如果玩家此时处于无敌状态，则该几率为100%。</description>
		</skill>
		
		<skill name="虚无狂暴">
			<name>YouthWolf_crazy</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>虚无狂暴</cnName>
			<conditionType>passive</conditionType>
			<condition>no</condition>
			<target>me</target>
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>0.1</duration>
			
			<pointEffectImg con="add" soundUrl="sound/fireHit2">bulletHitEffect/headshot</pointEffectImg>
			<otherEffectImg con="add" soundUrl="sound/crazy_hero">bulletHitEffect/purpleLaser</otherEffectImg>
			<description>生命值低于30%时进入虚无狂暴状态，此时只接受副手攻击。</description>
		</skill>
		
					<skill cnName="只受副手攻击"><!-- 生存-被动 -->
						<name>YouthWolfUnder</name>
						<cnName>只受副手攻击</cnName><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>underHit</condition>
						<target>target</target>
						<!--效果------------------------------------------------------------ -->
						<addType>instant</addType>
						<effectType>YouthWolfUnder</effectType>
						<mul>0.03</mul>
						<description>只受副手攻击</description>
					</skill>
					<skill cnName="只受副手攻击2"><!-- 生存-被动 -->
						<name>YouthWolfUnder2</name>
						<cnName>只受副手攻击2</cnName><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>underSkillHit</condition>
						<target>target</target>
						<!--效果------------------------------------------------------------ -->
						<addType>instant</addType>
						<effectType>YouthWolfUnder</effectType>
						<mul>0.03</mul>
						<description>只受副手攻击</description>
					</skill>
					
					<skill index="0" name="让主角副手怒气回复速度增加"><!-- dps -->
						<name>YouthWolfHeroAngerAdd</name>
						<cnName>激怒</cnName>
						<!--英雄技能属性------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>interval</condition>
						<target>me,range,enemy</target>
						<!--效果------------------------------------------------------------ -->
						<addType>state</addType>
						<effectType>angerAddMul</effectType>
						<mul>6</mul>
						<duration>1</duration>
						<range>99999</range>
						<description>所有敌人的怒气回复速度增加6倍。</description>
					</skill>
	</father>
	
</data>