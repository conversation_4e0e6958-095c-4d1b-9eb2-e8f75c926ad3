<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="aircraft" cnName="战车定义">
		<equip name="FlyDragonAir" cnName="异祖龙" rideLabel="RedMotoRide"  defineType="normal" evolutionLabel="FrozenDragonAir">
			<evolutionLv>2</evolutionLv>
			<lifeMul>2.5</lifeMul>
			<attackMul>5.5</attackMul><attackActionLabel>sprintAttack</attackActionLabel>
			<duration>80</duration>
			<cd>120</cd>
			<addObjJson>{'dpsAll':0.22,'lifeAll':0.20}</addObjJson>
			<skillArr>murderous_vehicle,WatchEagleAirDefence,vehicleFit_Gaia</skillArr>
		</equip>
		<equip name="FrozenDragonAir" cnName="冰霜祖龙" rideLabel="RedMotoRide"  defineType="normal">
			<evolutionLv>4</evolutionLv><mustCash>50</mustCash>
			<lifeMul>3</lifeMul>
			<attackMul>6.5</attackMul><attackActionLabel>sprintAttack</attackActionLabel>
			<duration>80</duration>
			<cd>120</cd>
			<addObjJson>{'dpsAll':0.24,'lifeAll':0.20}</addObjJson>
			<skillArr>murderous_vehicle,WatchEagleAirDefence,vehicleFit_Gaia</skillArr>
		</equip>
	</father>
	
	
	
	
	
	
	<father name="vehicle" cnName="战车body">
		<body index="0" name="异祖龙飞行器" shell="normal">
			
			<name>FlyDragonAir</name>
			<cnName>异祖龙飞行器</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/vehicle/FlyDragonAir110.swf</swfUrl>
			<!-- 图像 -->
			<imgType>normal</imgType>
			<flipCtrlBy>mouse</flipCtrlBy>
			<showLevel>999</showLevel>
			<imgArr>
				stand,move,hurt1,die1
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
				,sprintAttack
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-27,-23,50,54</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>15</maxVx>
			<motionState>fly</motionState><flyUseSpiderB>1</flyUseSpiderB><flyType>space</flyType>
			<motionD F_AIR="3" />
			
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel>FlyDragonAir_AIExtra</extraAIClassLabel>
			<!-- 技能 -->
			<bulletLauncherClass>VehicleBulletLauncher</bulletLauncherClass>
			<keyClass>VehicleBodyKey</keyClass>
			<bossSkillArrCn></bossSkillArrCn>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>sprintAttack</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<hurtRatio>0.8</hurtRatio><meBack>35</meBack>
					<grapRect>-350,-17,270,97</grapRect>
					<attackType>through</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl soundUrl="sound/body_hit">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				
			</hurtArr>
		</body>
		
		<body name="冰霜祖龙" fixed="FlyDragonAir" shell="normal">
			<name>FrozenDragonAir</name>
			<cnName>冰霜祖龙</cnName>
			<swfUrl>swf/vehicle/FrozenDragonAir.swf</swfUrl>
			<bmpUrl>BodyImg/FrozenDragonAir</bmpUrl>
		</body>
	</father>
</data>