<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="enemy">
		<body name="毒魔">
			
			<name>PoisonDemon</name>
			<cnName>毒魔</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/enemy/PoisonDemon.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1.8</lifeRatio>
			<rateRatio>0.05</rateRatio>
			<showLevel>999</showLevel>
			<!-- 图像 -->
			<imgType>normal</imgType>
			<showLevel>91</showLevel>
			<imgArr>
				stand,move,run
				,normalAttack,shakeAttack,pullAttack,poisonAttack
				,hurt1,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<lifeBarExtraHeight>-50</lifeBarExtraHeight>
			<handAddRa>90</handAddRa>
			<!-- 碰撞体积 -->
			<hitRect>-18,-96,36,96</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>12</maxVx>
			<runStartVx>8</runStartVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel>PoisonDemon_AIExtra</extraAIClassLabel>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr>metalCorrosion,strong_enemy,PoisonDemonJump,cmldef_enemy,fightReduct2,defenceBounce_enemy,PoisonDemonElves,PoisonDemonElvesShow</bossSkillArr>
			<bossSkillArrCn>60%近战防御、80%防毒、胶性表皮</bossSkillArrCn>
			<extraG label="ExtraTaskAI" bossSkillArr="metalCorrosion,strong_enemy,PoisonDemonJump,cmldef_enemy,fightReduct2,defenceBounce_enemy,PoisonDemonElves,PoisonDemonElvesShow">
				<extra lifeMin="0.7" skillArr=""/>
				<extra lifeMin="0.4" skillArr=""/>
				<extra lifeMin="0" skillArr=""/>
			</extraG>
			<extraDropArmsB>1</extraDropArmsB>
			<dropD blackArmsBodyB="1"/>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack</imgLabel>
					<hurtRatio>5</hurtRatio>
					<attackType>direct</attackType>
					<skillArr></skillArr>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit4">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt info="不加入ai选择">
					<imgLabel>shakeAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>3</hurtRatio>
					<attackType>holy</attackType>
					<skillArr>PoisonDemonHit</skillArr>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit4">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt cnName="抓捕"><![CDATA[将敌人抓捕到毒魔面前，清除敌人所有枪支弹药。]]>
					<imgLabel>pullAttack</imgLabel><mustGrapRectB>1</mustGrapRectB>
					<hurtRatio>0.001</hurtRatio>
					<skillArr>pullAttackHit,pullAttackHammer,pullAttackEmp</skillArr>
					<attackType>holy</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit4">bulletHitEffect/energy</hitImgUrl>
					<noUseOtherSkillB>1</noUseOtherSkillB>
				</hurt>
			</hurtArr>
		</body>	
		
		<skill index="0" name="闪击">
			<name>PoisonDemonJump</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>闪击</cnName>
			<cd>3</cd>
			<conditionType>active</conditionType><condition>avtiveSkillCdOver</condition>
			<otherConditionArr>nearAttackTarget</otherConditionArr>
			<conditionRange>700</conditionRange><target>me</target>
			
			<addType>state</addType>
			<effectType>invincible</effectType>
			<duration>1.1</duration>
			<meActionLabel>shakeAttack</meActionLabel>
			<description>闪烁到敌人面前，对其发起震地攻击，减速敌人，减速之前敌人移动速度越快则减速时间越长。</description>
		</skill>
					<skill name="闪击-减速目标">
						<name>PoisonDemonHit</name>
						<cnName>闪击-减速目标</cnName>
						<noBeClearB>1</noBeClearB>
						<condition>hit</condition>
						<conditionType>passive</conditionType>
						<target>target</target>
						<addType>state</addType>
						<effectType>slowByMoveSpeed</effectType>
						<value>1</value><![CDATA[最短减速时间]]>
						<mul>0.35</mul><![CDATA[减速时间=mul*移动速度]]>
						<duration>6</duration><![CDATA[最长减速时间]]>
						<!--图像------------------------------------------------------------ -->
						<stateEffectImg partType="2foot">skillEffect/disabled_enemy</stateEffectImg>
					</skill>
					
					
		<skill index="0" name="抓捕-击中"><!-- dps -->
			<name>pullAttackHit</name>
			<cnName>抓捕-击中</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>SnowGirlPullHit</effectType>
			<mul>18</mul><![CDATA[开始帧]]>
			<secMul>24</secMul><![CDATA[结束帧]]>
			<valueString>hand_left</valueString>
			<duration>0.2</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg partType="mouth" con="add">skillEffect/dizziness</stateEffectImg>
		</skill>
					<skill index="0" cnName="击中眩晕"><!-- 限制 -->
						<name>pullAttackHammer</name>
						<cnName>击中眩晕</cnName>
						<showInLifeBarB>1</showInLifeBarB>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>hit</condition>
						<target>target</target>
						<!--效果------------------------------------------------------------ -->
						<addType>instantAndState</addType>
						<effectType>dizziness</effectType>
						<duration>2</duration>
						<!--图像------------------------------------------------------------ -->
						<stateEffectImg partType="mouth" con="add">skillEffect/dizziness</stateEffectImg>
						<description>击中使目标陷入眩晕状态，持续[duration]秒。</description>
					</skill>
					<skill index="0" name="清空目标子弹"><!-- dps -->
						<name>pullAttackEmp</name>
						<cnName>清空目标子弹</cnName>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>hit</condition>
						<target>target</target>
						<!--效果------------------------------------------------------------ -->
						<addType>instant</addType>
						<effectType>emp</effectType>
						<value>1</value><!-- 弹容 -->
						<mul>0</mul><!-- 携弹量 -->
						<!--图像------------------------------------------------------------ -->
						<description>击中目标后清空其所有枪支的子弹。</description>
					</skill>
					
		<skill index="0" name="毒精灵">
			<name>PoisonDemonElves</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>毒精灵</cnName>
			<cd>20</cd>
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>lifePerLess</otherConditionArr>
			<conditionRange>0.35</conditionRange>
			<target>me</target>
			<addType>state</addType>
			<effectType>no</effectType>
			<meActionLabel>poisonAttack</meActionLabel>
			<description>毒魔生命值少于35%时会吐出毒精灵（技能免疫），被毒精灵碰撞的敌人会降低攻击力并受到七步毒伤害。</description>
		</skill>
					<skill index="0" cnName="召唤毒精灵"><!-- 限制 -->
						<name>PoisonDemonElvesShow</name>
						<cnName>召唤毒精灵</cnName>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>active</conditionType>
						<target>me</target>
						<!--效果------------------------------------------------------------ -->
						<addType>instant</addType>
						<effectType>summonedUnits</effectType>
						<!-- 子弹所需 -->
						<obj>"cnName":"毒精灵","num":1,"lifeMul":0.015,"dpsMul":0.5,"mulByFatherB":1,"cx":-100,"cy":-97,"firstVx":10,"maxNum":10,"skillArr":["posion7_wolf","disabled_enemy","State_SpellImmunity"]</obj>
						<duration>30</duration>
						<!--图像------------------------------------------------------------ -->
						<description></description>
					</skill>
		
	</father>
	
	<father name="other" cnName="其他">
		<body index="0" name="毒精灵">
			
			<name>PoisonElves</name>
			<cnName>毒精灵</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/enemy/PoisonElves.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>0.7</lifeRatio>
			<!-- 图像 -->
			<dieJumpMul>0</dieJumpMul>
			<imgArr>
				stand,move,die1
				,__jumpUp,jumpUp,jumpDown,jumpDown__
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-14,-30,28,30</hitRect>
			<!-- 运动 -->
			<maxVx>9</maxVx>
			<motionD F_G="0.3" jumpDelayT="0.001"/>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel>Antimatter_AIExtra</extraAIClassLabel>
			<!-- 技能 -->
			<skillArr></skillArr>
			<avtiveSkillCdOverT>0</avtiveSkillCdOverT>
			<!-- 攻击数据 -->
			<hurtArr>
				
				<hurt>
					<imgLabel>move</imgLabel>
					<hurtRatio>0.000001</hurtRatio>
					<shakeValue>4</shakeValue>
					<attackType>direct</attackType>
				</hurt>
				<hurt><imgLabel>stand</imgLabel><noAiChooseB>1</noAiChooseB><hurtRatio>0.000001</hurtRatio><attackType>direct</attackType></hurt>
				<hurt><imgLabel>jumpUp</imgLabel><noAiChooseB>1</noAiChooseB><hurtRatio>0.000001</hurtRatio><attackType>direct</attackType></hurt>
				<hurt><imgLabel>jumpDown</imgLabel><noAiChooseB>1</noAiChooseB><hurtRatio>0.000001</hurtRatio><attackType>direct</attackType></hurt>
				<hurt><imgLabel>__jumpUp</imgLabel><noAiChooseB>1</noAiChooseB><hurtRatio>0.000001</hurtRatio><attackType>direct</attackType></hurt>
				<hurt><imgLabel>jumpDown__</imgLabel><noAiChooseB>1</noAiChooseB><hurtRatio>0.000001</hurtRatio><attackType>direct</attackType></hurt>
			</hurtArr>
		</body>	
	</father>
</data>