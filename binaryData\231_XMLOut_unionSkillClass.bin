<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="unionSkill" cnName="公会技能">
		
		<skill index="0" name="怪物猎人"><!-- dps-被动 -->
			<name>watchmanHurtNormal</name>
			<cnName>怪物猎人</cnName><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurt</effectType>
			<valueString>normal</valueString>
			<mul>1.10</mul>
			<!-- 修改伤害所需 -->
			<obj>"type":"enemyType"</obj>
			<!--图像------------------------------------------------------------ -->
			<description>对普通怪物造成的伤害提高[mul-1]。</description>
		</skill>
		<skill index="0" name="精英克星"><!-- dps-被动 -->
			<name>watchmanHurtSuper</name>
			<cnName>精英克星</cnName><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurt</effectType>
			<valueString>super</valueString>
			<mul>1.10</mul>
			<!-- 修改伤害所需 -->
			<obj>"type":"enemyType"</obj>
			<!--图像------------------------------------------------------------ -->
			<description>对精英怪物造成的伤害提高[mul-1]。</description>
		</skill>
		<skill index="0" name="人类克星"><!-- dps-被动 -->
			<name>watchmanHurtHuman</name>
			<cnName>人类克星</cnName><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<otherConditionArr>raceType</otherConditionArr>
			<conditionString>human</conditionString>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurtNoCondition</effectType>
			<mul>1.1</mul>
			<!--图像------------------------------------------------------------ -->
			<description>对人类造成额外[mul-1]的伤害。</description>
		</skill>
		
		
		<skill index="0" name="恶魔猎手"><!-- dps-被动 -->
			<name>watchmanHurtBoss</name>
			<cnName>恶魔猎手</cnName><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurt</effectType>
			<valueString>boss</valueString>
			<mul>1.10</mul>
			<!-- 修改伤害所需 -->
			<obj>"type":"enemyType"</obj>
			<!--图像------------------------------------------------------------ -->
			<description>对首领造成的伤害提高[mul-1]。</description>
		</skill>
		
		<skill index="0" name="恶魔屠刀"><!-- dps-被动 -->
			<name>superWatchmanHurtBoss</name>
			<cnName>恶魔屠刀</cnName><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurt</effectType>
			<valueString>boss</valueString>
			<mul>1.20</mul>
			<!-- 修改伤害所需 -->
			<obj>"type":"enemyType"</obj>
			<!--图像------------------------------------------------------------ -->
			<description>对首领造成的伤害提高[mul-1]。</description>
		</skill>
		
		<![CDATA[连击技能]]>
		<skill cnName="上帝之眼"><!-- dps -->
			<name>godEyes</name>
			<cnName>上帝之眼</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>skillCombo</condition>
			<otherConditionArr>skillCombo</otherConditionArr>
			<conditionRange>9</conditionRange>
			<conditionString>poisonousFog_hero,feedback_hero,pointBoom_hero</conditionString>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>bullet_godEyes</effectType>
			<extraValueType>nowArmsTrueDps</extraValueType><!-- 附加值类型为当前武器dps比例 -->
			<range>600</range>
			<value>0.2</value>
			<duration>0.2</duration>
			<!-- 子弹所需 -->
			<obj>"name":"godEyes"</obj>
			<!--图像------------------------------------------------------------ -->
			<description>同时触发人物技能毒雾、电离折射、定点轰炸（3个技能等级要求为9级），将启动二元化学激光，原本无毒的化学激光交叉碰撞后，迅速产生光化学毒素，伤害范围内的人并破坏他们10%的防御力，持续20秒。</description>
		</skill>
		
		<skill cnName="上帝之杖"><!-- dps -->
			<name>godMace</name>
			<cnName>上帝之杖</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>skillCombo</condition>
			<otherConditionArr>skillCombo</otherConditionArr>
			<conditionRange>9</conditionRange>
			<conditionString>crazy_hero,tenacious_hero,through_hero</conditionString>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>bullet_godMace</effectType>
			<extraValueType>nowArmsTrueDps</extraValueType><!-- 附加值类型为当前武器dps比例 -->
			<range>600</range>
			<value>0.2</value>
			<duration>2</duration>
			<!-- 子弹所需 -->
			<obj>"name":"godMace"</obj>
			<pointEffectImg raNum="1" soundUrl="sound/laserShoot2">bulletHitEffect/purpleLaser</pointEffectImg>
			<!--图像------------------------------------------------------------ -->
			<description>同时触发人物技能狂暴、反击、金刚钻（3个技能等级要求为9级），将引导太空中的天基动能卫星，向地面发射巨大的金属柱，形成人造陨石轰击地面，造成堪比核弹的灾难性打击，冲击波能让敌人降低20%防御力，持续20秒。</description>
		</skill>
		
		
		<skill cnName="鬼步"><!-- dps -->
			<name>heroSprint</name>
			<cnName>鬼步</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>moveCombo</condition>
			<otherConditionArr>airMoveCombo</otherConditionArr>
			<conditionString>left,left|right,right</conditionString>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>heroSprint</effectType>
			<mul>0</mul>
			<value>35</value>
			<duration>0.3</duration>
			<!-- 子弹所需 -->
			<stateEffectImg xGap="30" con="filter" raNum="1">skillEffect/sprintShadow</stateEffectImg>
			<!--图像------------------------------------------------------------ -->
			<description>人物在空中，双击前进或者后退按钮，可以向指定方向冲刺一段距离。</description>
		</skill>
		
		<skill index="0" cnName="上帝之眼-防御力降低"><!-- 生存 -->
			<name>godEyesDefence</name>
			<cnName>上帝之眼-防御力降低</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>underHurtMul</effectType>
			<mul>1.1</mul><!-- 重生后生命值 -->
			<duration>20</duration>
			<!--图像------------------------------------------------------------ -->
			<description>击中目标后削弱其[mul-1]的防御力。</description>
		</skill>
		<skill index="0" cnName="上帝之杖-防御力降低"><!-- 生存 -->
			<name>godMaceDefence</name>
			<cnName>上帝之杖-防御力降低</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>underHurtMul</effectType>
			<mul>1.2</mul><!-- 重生后生命值 -->
			<duration>20</duration>
			<!--图像------------------------------------------------------------ -->
			<description>击中目标后削弱其[mul-1]的防御力。</description>
		</skill>
		
	</father>
</data>