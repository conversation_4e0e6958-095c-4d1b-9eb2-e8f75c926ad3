<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="car" cnName="战车定义">
		<equip name="AircraftGun" cnName="狩猎者">
			<canComposeB>1</canComposeB>
			<mustSightMul>0.3</mustSightMul>
			<main label="AircraftGun_main" dpsMul="1.6" len="136" yGap="7" minRa="170" maxRa="20" />
			<sub label="Diggers_sub" hideB="1" dpsMul="0" />
			<lifeMul>2</lifeMul>
			<attackMul>1</attackMul>
			<duration>60</duration>
			<cd>120</cd>
			<mustCash>300</mustCash>
			<addObjJson>{'dpsAll':0.08,'lifeAll':0.08}</addObjJson>
		</equip>
		
		<bullet cnName="狩猎者">
			<name>AircraftGun_main</name>
			<cnName>狩猎者</cnName>
			<!--武器属性------------------------------------------------------------ -->
			<hurtRatio>2</hurtRatio>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>1</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>30</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.35</attackGap>
			<attackDelay>0.2</attackDelay>
			
			<!--运动属性------------------------------------------------------------ -->	
			<shootRecoil>15</shootRecoil>
			<screenShakeValue>16</screenShakeValue>
			<bulletSpeed>35</bulletSpeed>
			<boomD  bodyB="1" floorB="1" radius="120"/>
			<penetrationGap>1000</penetrationGap>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<fireImgUrl soundUrl="AircraftGun/shoot"></fireImgUrl>
			<bulletImgUrl raNum="30" con="filter">AircraftGun/bullet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/magicHit2"  shake="2,0.2,10" con="add">AircraftGun/hit</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
	</father>
	
	
	
	
	
	
	<father name="vehicle" cnName="战车body">
		<body index="0" name="狩猎者" shell="metal">
			
			<name>AircraftGun</name><cnName>狩猎者</cnName><raceType>robot</raceType>
			<swfUrl>swf/vehicle/AircraftGun.swf</swfUrl>
			<!-- 图像 -->
			<lifeBarExtraHeight>-20</lifeBarExtraHeight>
			
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg><dieJumpMul>0</dieJumpMul>
			<imgClass>CarImage</imgClass><imgType>normal</imgType><rotateBySlopeB>1</rotateBySlopeB>
			<imgArr>
				stand,die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-30,-88,60,88</hitRect>
			<!-- 运动 -->
			<motionD F_G="0.8" F_I="0.8" F_F="0.9" moveWhenVB="0" />
			<maxVx>0</maxVx><maxJumpNum>0</maxJumpNum>
			<!-- 技能 -->
			<attackAIClass>CarAttack_AI</attackAIClass>
			<keyClass>CarBodyKey</keyClass>
			<bulletLauncherClass>CarBulletLauncher</bulletLauncherClass>
			<skillArr></skillArr>
			<!-- 攻击数据 -->
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>stand</imgLabel>
					<hurtRatio>0.4</hurtRatio><attackType>through</attackType><shakeValue>10</shakeValue><meBack>0</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit4">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>
	</father>
</data>