<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="wilder">
		<body index="0" name="多肉">
			
			<name>MeatyZombie</name>
			<cnName>多肉</cnName>
			<raceType>zombies</raceType>
			<swfUrl>swf/enemy/MeatyZombie.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1.3</lifeRatio>
			<showLevel>999</showLevel>
			<!-- 图像 -->
			<imgType>normal</imgType>
			<lifeBarExtraHeight>-30</lifeBarExtraHeight>
			<imgArr>
				stand,move,run
				,normalAttack,thumpAttack,shieldAttack,magicAttack
				,hurt1,hurt2,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-18,-86,36,86</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>10</maxVx>
			<runStartVx>8</runStartVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr>MeatySkillBack,cmldef2_enemy,defenceBounce_enemy,MeatyDesertedHalo,MeatyAway,MeatyShieldUnder,MeatyShield,desertedHalo_enemy</bossSkillArr>
			<bossSkillArrCn></bossSkillArrCn>
			<extraAIClassLabel>MeatyZombie_AIExtra</extraAIClassLabel>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack</imgLabel>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
					<beatBack>3</beatBack>
					<shakeValue>6</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit4">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt info="不加入ai选择" cd="5">
					<imgLabel>thumpAttack</imgLabel>
					<hurtMul>0.05</hurtMul>
					<attackType>holy</attackType>
					<beatBack>3</beatBack>
					<shakeValue>6</shakeValue>
					<skillArr>MeatyThumpHit</skillArr>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit4">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>	
	</father>
	<father name="enemy">	
		
		<skill index="0" cnName="击中眩晕"><!-- 限制 -->
			<name>MeatyThumpHit</name>
			<cnName>击中眩晕</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>dizziness</effectType>
			<duration>0.6</duration>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="mouth" con="add">skillEffect/dizziness</stateEffectImg>
			<description></description>
		</skill>
		<skill cnName="全域驱赶"><!-- dps -->
			<name>MeatyAway</name>
			<cnName>全域驱赶</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cd>6</cd>
			<delay>0.3</delay>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>lifePerLess</otherConditionArr>
			<conditionRange>0.3</conditionRange>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>hurtMul</effectType>
			<mul>0.15</mul>
			<duration>6</duration>
			<range>99999</range>
			<!--图像------------------------------------------------------------ --> 
			<targetEffectImg partType="body">boomEffect/posion3</targetEffectImg>
			<meActionLabel>magicAttack</meActionLabel>
			<description>生命低于30%时，每隔[cd]秒减少所有敌人[mul]的生命值。</description>
		</skill>
		<skill cnName="荒芜光环"><!-- dps -->
			<name>MeatyDesertedHalo</name><wantDescripB>1</wantDescripB><showInLifeBarB>1</showInLifeBarB>
			<cnName>荒芜光环</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>deserted</effectType>
			<value>0</value>
			<mul>0.8</mul>
			<duration>2</duration>
			<range>999999</range>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="2hand">skillEffect/disabled_enemy</stateEffectImg>
			<description>降低所有敌人20%的射击速度，同时使敌人的“击中回复”技能无效。</description>
		</skill>
		<skill cnName="技能反弹"><!-- dps -->
			<name>MeatySkillBack</name><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cnName>技能反弹</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType><noCopyB>1</noCopyB>
			<condition>underEnemySkill</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>skillBack</effectType>
			<effectProArr>0.8</effectProArr>
			<description>有80%几率反弹敌人的技能，让敌人获得同样的技能效果。</description>
		</skill>
		<![CDATA[--反击护盾--------------------------------]]>
		<skill cnName="反击护盾"><!-- dps -->
			<name>MeatyShield</name>
			<cnName>反击护盾</cnName><noBeClearB>1</noBeClearB>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<cd>12</cd>
			<delay>1.6</delay>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>lifePerLess</otherConditionArr>
			<conditionRange>0.6</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>underHurtMul</effectType>
			<mul>0.0000001</mul>
			<duration>9</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg partType="body" con="add">generalEffect/invincibleShield</stateEffectImg>
			<meActionLabel>shieldAttack</meActionLabel>
			<description>释放持续的反击护盾，此时受到攻击会释放反击粒子追踪敌人。反击粒子若碰到在空中的敌人（不包含空中单位），则会被转化成攻击多肉的粒子。</description>
		</skill>
		<skill cnName="受到攻击产生反击粒子"><!-- dps -->
			<name>MeatyShieldUnder</name>
			<cnName>受到攻击产生反击粒子</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHit</condition>
			<otherConditionArr>meHaveStateOr</otherConditionArr>
			<conditionString>MeatyShield</conditionString>
			<minTriggerT>0.3</minTriggerT>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet_MeatyShield</effectType>
			<!-- 子弹所需 -->
			<obj>"name":"MeatyShieldBullet","site":"hitPosition","flipB":false</obj>
		</skill>
					<skill cnName="反击粒子击中伤害"><!-- dps -->
						<name>MeatyShieldBulletHit</name>
						<cnName>反击粒子伤害</cnName><changeHurtB>1</changeHurtB>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>hit</condition>
						<target>target</target>
						<!--效果------------------------------------------------------------ -->
						<addType>instant</addType>
						<effectType>MeatyShieldBulletHit</effectType>
						<description>如果目标在空中，则对释放者造成伤害，如果目标在地面则对目标造成伤害。</description>
					</skill>
					<skill cnName="反击粒子击中反弹"><!-- dps -->
						<name>MeatyShieldBulletBack</name>
						<cnName>反击粒子伤害</cnName>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>hit</condition>
						<otherConditionArr>isAirB,isLandBody</otherConditionArr>
						<target>target</target>
						<!--效果------------------------------------------------------------ -->
						<addType>instant</addType>
						<effectType>bullet</effectType>
						<!-- 子弹所需 -->
						<obj>"name":"MeatyBackBullet","site":"hitPosition","flipB":false</obj>
					</skill>
					<skill cnName="反弹粒子对boss的伤害"><!-- dps -->
						<name>MeatyBackBulletHit</name>
						<cnName>反弹粒子对boss的伤害</cnName><changeHurtB>1</changeHurtB>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>hit</condition>
						<target>target</target>
						<!--效果------------------------------------------------------------ -->
						<addType>instant</addType>
						<effectType>onlyHurtMul</effectType>
						<mul>0.005</mul>
						<description></description>
					</skill>
					
					
					<bullet cnName="反击粒子">
						<name>MeatyShieldBullet</name>
						<cnName>反击粒子</cnName>
						<!--伤害属性------------------------------------------------------------ -->
						<hurtRatio>0</hurtRatio>
						<hurlMul>0</hurlMul>
						<attackType>holy</attackType>
						<!--基本属性------------------------------------------------------------ -->
						<bulletLife>13</bulletLife>
						<bulletWidth>25</bulletWidth>
						<hitType>rect</hitType>
						<!--攻击时的属性------------------------------------------------------------ -->
						<bulletAngle>180</bulletAngle>
						<!--特殊属性------------------------------------------------------------ -->	
						<followD value="1"/>
						<penetrationGap>1000</penetrationGap>
						<skillArr>MeatyShieldBulletHit,MeatyShieldBulletBack</skillArr>
						<!--运动属性------------------------------------------------------------ -->	
						<shootPoint>0,0</shootPoint>
						<bulletSpeed>13</bulletSpeed>
						<speedD random="0.5"/>
						<!--图像动画属性------------------------------------------------------------ -->
						<flipX>1</flipX>
						<bulletImgUrl con="add">MeatyZombie/shieldBullet</bulletImgUrl>
						<hitImgUrl soundUrl="sound/magicHit2">bulletHitEffect/fitHit</hitImgUrl>
					</bullet>
					<bullet cnName="反弹粒子">
						<name>MeatyBackBullet</name>
						<cnName>反弹粒子</cnName>
						<!--伤害属性------------------------------------------------------------ -->
						<hurtRatio>0</hurtRatio>
						<hurlMul>0</hurlMul>
						<attackType>holy</attackType>
						<!--基本属性------------------------------------------------------------ -->
						<bulletLife>13</bulletLife>
						<bulletWidth>25</bulletWidth>
						<hitType>rect</hitType>
						<!--攻击时的属性------------------------------------------------------------ -->
						<bulletAngle>180</bulletAngle>
						<!--特殊属性------------------------------------------------------------ -->	
						<followD value="1"/>
						<penetrationGap>1000</penetrationGap>
						<skillArr>MeatyBackBulletHit</skillArr>
						<!--运动属性------------------------------------------------------------ -->	
						<shootPoint>0,0</shootPoint>
						<bulletSpeed>15</bulletSpeed>
						<speedD random="0.2"/>
						<!--图像动画属性------------------------------------------------------------ -->
						<flipX>1</flipX>
						<bulletImgUrl con="add">MeatyZombie/backBullet</bulletImgUrl>
						<hitImgUrl soundUrl="sound/water_hit">bulletHitEffect/fitHit</hitImgUrl>
					</bullet>
					
					
					
		
	</father>	
	
	
</data>