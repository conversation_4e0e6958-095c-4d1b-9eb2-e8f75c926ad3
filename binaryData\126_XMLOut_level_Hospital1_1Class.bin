<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="normal">
		<gather name="实验室">
			
			<level name="Hospital1_plot">
				<!-- 发兵集************************************************ -->
				<info enemyLv="96"/>
				<fixed target="Hospital1_1" info="no" drop="all" unitG="all" rectG="all" eventG="no"/>
				<!-- 基本属性 -->
				<sceneLabel>Hospital1</sceneLabel>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e1_1"><condition delay="2"></condition><order>say; startList:s1</order></event>
						<event id="e2_0"><condition>say:listOver; s1</condition></event><event id="e2_1"><condition delay="1"></condition></event>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order></event> 
						<event id="e2_11"><condition delay="1">enemyNumber:less_1</condition><order>level; rebirthAllMore</order></event>	
						<event id="e2_11"><condition delay="1"></condition><order>say; startList:s2</order></event>	
						<event id="e2_11">
							<condition delay="0.1">say:listOver; s2</condition>
							<order>task:now; complete</order>
							<order>worldMap:levelName; Hospital1:Hospital1_1</order>
							<order>level; showPointer:r_over</order>
						</event>	
					</group>
				</eventG>
			</level>
			
			<level name="Hospital1_1">
				<!-- 发兵集************************************************ -->
				<info enemyLv="96" diff="2" />
				<sceneLabel>Hospital1</sceneLabel>
				<!-- 发兵集************************************************ -->
				<unitG><allDefault aiOrder="patrolGlobal"></allDefault>
					<unitOrder id="enemy1">
						<numberType>number</numberType>
						<unit cnName="伏地尸" num="4"/>
						<unit cnName="独眼僵尸" num="6"/>
					</unitOrder>
					<unitOrder id="enemy2">
						<numberType>number</numberType>
						<unit cnName="独眼僵尸" num="9"/>
						<unit cnName="伏地尸" num="3"/>
						
					</unitOrder>
					<unitOrder id="enemy3">
						<numberType>number</numberType>
						<unit cnName="独眼僵尸" num="8"/>
						<unit cnName="伏地尸" num="6"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<numberType>number</numberType>
						<unit cnName="看门狗" unitType="boss" lifeMul="3" dpsMul="2"/>
					</unitOrder>
				</unitG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event id="e2_1"><condition delay="1"></condition></event>
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy1; r1</order><order>createUnit:enemy1; r2</order><order>createUnit:enemy1; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy2; r1</order><order>createUnit:enemy2; r2</order><order>createUnit:enemy2; r3</order></event> 
						<event id="e2_1"><condition doNumber="3" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy3; r1</order><order>createUnit:enemy3; r2</order><order>createUnit:enemy3; r3</order></event> 
						<event id="e2_1"><condition doNumber="1" orderChooseType="randomOne">enemyNumber:less_3</condition><order>createUnit:enemy4; r1</order><order>createUnit:enemy4; r2</order><order>createUnit:enemy4; r3</order></event> 
						<event id="e2_11">
							<condition>enemyNumber:less_1</condition>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
				</eventG>
			</level>
		</gather>
	</father>
</data>