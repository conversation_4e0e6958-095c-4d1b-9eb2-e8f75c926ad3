<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="enemy">
		<body name="狂人机器X" shell="compound">
			
			<name>WarriorSec</name>
			<cnName>狂人机器X</cnName><headIconUrl>IconGather/WarriorSec</headIconUrl>
			<raceType>robot</raceType>
			<swfUrl>swf/enemy/WarriorSec.swf</swfUrl>
			<!-- 基本系数 -->
			<showLevel>999</showLevel>
			<lifeRatio>1.8</lifeRatio>
			<rosRatio>0.5</rosRatio>
			<!-- 图像 -->
			<imgType>normal</imgType>
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<dieJumpMul>0</dieJumpMul>
			<imgArr>
				stand,move,run
				,normalAttack,shootAttack,shakeAttack
				,hurt1,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<lifeBarExtraHeight>-120</lifeBarExtraHeight>
			<handAddRa>90</handAddRa>
			<!-- 碰撞体积 -->
			<hitRect>-18,-96,36,96</hitRect>
			<!-- 运动 -->
			<maxJumpNum>2</maxJumpNum>
			<maxVx>12</maxVx>
			<runStartVx>8</runStartVx>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			
			<!-- 技能 -->
			<skillArr></skillArr>
			<bossSkillArr>groupCrazy_enemy,WarriorShoot,WarriorSprint,State_SpellImmunity,fightReduct2,rigidBody_enemy,summSentry,likeMissle_Shapers,findHide</bossSkillArr>
			<extraDropArmsB>1</extraDropArmsB>
			<extraAIClassLabel>Warrior_AIExtra</extraAIClassLabel>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>move</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.3</hurtRatio>
					<attackType>through</attackType>
					<meBack>3</meBack><hitMaxNum>1</hitMaxNum>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit2" volume="0.3">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>run</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>0.3</hurtRatio>
					<attackType>through</attackType>
					<meBack>3</meBack><hitMaxNum>1</hitMaxNum>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit2" volume="0.3">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>normalAttack</imgLabel><cn>竖劈</cn>
					<hurtRatio>2</hurtRatio>
					<attackType>direct</attackType>
					<skillArr></skillArr>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/metal_hit2" volume="0.5">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shootAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>3</hurtRatio>
					<attackType>holy</attackType>
					<bulletLabel>WarriorSecBullet</bulletLabel>
					<grapRect>-650,-200,380,200</grapRect>
					<skillArr>silenceWarrior,noMoveWarrior</skillArr>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/metal_hit1">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shakeAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<hurtRatio>8</hurtRatio>
					<attackType>holy</attackType>
					<skillArr></skillArr>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/metal_hit1">bulletHitEffect/fitHit</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>	
		
		<bullet cnName="剑气">
			<name>WarriorSecBullet</name>
			<cnName>狂人机器X剑气</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>5</shakeAngle>
			<bulletLife>7</bulletLife>
			<bulletWidth>70</bulletWidth>
			<hitType>rect</hitType>
			<skillArr>silenceWarrior</skillArr>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.6</attackGap>
			<attackDelay>0.43</attackDelay>
			<bulletAngle>179.9</bulletAngle>
			<bulletAngleRange>5</bulletAngleRange>
			<bulletNum>1</bulletNum>				
			<shootNum>1</shootNum>				<!-- 1个攻击间隔内的射击次数（默认值为1）-
			->	
								
			<shootAngle>0</shootAngle>					
			<!--运动属性------------------------------------------------------------ -->	
			<twoHitGap>0.5</twoHitGap>
			<shootPoint>-300,-74</shootPoint>
			<bulletSpeed>35</bulletSpeed>
			<penetrationNum>999</penetrationNum>
			<penetrationGap>1000</penetrationGap>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="2" con="filter">WarriorSec/bullet</bulletImgUrl>
			<hitImgUrl con="add" soundUrl="sound/vehicle_hit1">bulletHitEffect/fitHit</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		
		
		
		<skill cnName="召唤哨兵">
			<name>summSentry</name>
			<cnName>召唤哨兵</cnName><iconUrl36>SkillIcon/summSentry_36</iconUrl36>
			<cd>20</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<summonedUnitsB>1</summonedUnitsB>
			<effectType>summonedUnits</effectType>
			<duration>40</duration>
			<!-- 子弹所需 -->
			<obj>"cnName":"哨兵","num":1,"lifeMul":0.15,"dpsMul":0.1,"mulByFatherB":1,"maxNum":4,"skillArr":["State_SpellImmunity","rigidBody_enemy"]</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/cloned_enemy"></meEffectImg>
			<description>召唤哨兵</description>
		</skill>
		
		<![CDATA[队友变身载具]]>
		<skill cnName="变身盖亚">
			<name>changeToGaia</name>
			<cnName>变身盖亚</cnName>
			<cd>90</cd><![CDATA[90]]>
			<firstCd>89</firstCd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>lifePerLess</otherConditionArr>
			<conditionRange>0.1</conditionRange><![CDATA[0.1]]>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<summonedUnitsB>1</summonedUnitsB>
			<effectType>summonedAndParasitic</effectType>
			<duration>30</duration>
			<!-- 子弹所需 -->
			<obj>"cnName":"盖亚","num":1,"lifeMul":0.15,"dpsMul":1,"mulByFatherB":1,"maxNum":1,"skillArr":["State_SpellImmunity"]</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg noFollowB="1" soundUrl="sound/vehicleFit" con="add">generalEffect/vehicleFit</meEffectImg>
			<description>变身盖亚</description>
		</skill>
		<skill cnName="变身异齿虎">
			<name>changeToSaberTiger</name>
			<cnName>变身异齿虎</cnName>
			<cd>90</cd><![CDATA[90]]>
			<firstCd>89</firstCd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>lifePerLess</otherConditionArr>
			<conditionRange>0.1</conditionRange><![CDATA[0.1]]>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<summonedUnitsB>1</summonedUnitsB>
			<effectType>summonedAndParasitic</effectType>
			<duration>30</duration>
			<!-- 子弹所需 -->
			<obj>"cnName":"异齿虎","num":1,"lifeMul":0.15,"dpsMul":1,"mulByFatherB":1,"maxNum":1,"skillArr":["State_SpellImmunity"]</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg noFollowB="1" soundUrl="sound/vehicleFit" con="add">generalEffect/vehicleFit</meEffectImg>
			<description>变身异齿虎</description>
		</skill>
		<skill cnName="变身雷霆">
			<name>changeToThunderbolt</name>
			<cnName>变身雷霆</cnName>
			<cd>90</cd><![CDATA[90]]>
			<firstCd>89</firstCd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>lifePerLess</otherConditionArr>
			<conditionRange>0.1</conditionRange><![CDATA[0.1]]>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<summonedUnitsB>1</summonedUnitsB>
			<effectType>summonedAndParasitic</effectType>
			<duration>30</duration>
			<!-- 子弹所需 -->
			<obj>"cnName":"雷霆","num":1,"lifeMul":0.15,"dpsMul":1,"mulByFatherB":1,"maxNum":1,"skillArr":["State_SpellImmunity"]</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg noFollowB="1" soundUrl="sound/vehicleFit" con="add">generalEffect/vehicleFit</meEffectImg>
			<description>变身雷霆</description>
		</skill>
		<skill cnName="变身挖掘者">
			<name>changeToSecDiggers</name>
			<cnName>变身挖掘者</cnName>
			<cd>90</cd><![CDATA[90]]>
			<firstCd>89</firstCd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<otherConditionArr>lifePerLess</otherConditionArr>
			<conditionRange>0.1</conditionRange><![CDATA[0.1]]>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<summonedUnitsB>1</summonedUnitsB>
			<effectType>summonedAndParasitic</effectType>
			<duration>30</duration>
			<!-- 子弹所需 -->
			<obj>"cnName":"腥红挖掘者","num":1,"lifeMul":0.15,"dpsMul":1,"mulByFatherB":1,"maxNum":1,"skillArr":["State_SpellImmunity"]</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg noFollowB="1" soundUrl="sound/vehicleFit" con="add">generalEffect/vehicleFit</meEffectImg>
			<description>变身腥红挖掘者</description>
		</skill>
	</father>
	
	
	
	
	
</data>