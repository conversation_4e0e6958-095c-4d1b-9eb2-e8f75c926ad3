<?xml version="1.0" encoding="utf-8" ?>
<data>
	<gather name="heroAI">
		<father name="heroAI_normal" cnName="基本属性">
			
			<body cnName="寻敌范围" name="warningRange" 					type="number" min="10" 	max="3000" fixed="0" tip="寻找敌人的范围距离。" />
			
			<body cnName="警戒范围" name="keepGap" 							min="10" 	max="10000" fixed="0" tip="与敌人保持的距离。" />
			<body cnName="警戒数量" name="keepNum" 							min="0" 	max="1000" fixed="0" tip="允许警戒范围内敌人的个数。" />
			
			<body cnName="躲避攻击Buff" name="awayAttackBuffB"  tip="敌人开启攻击Buff时远离他们。" />
			<body cnName="乱跳频率" name="jumpPro" 							min="0" 	max="1" fixed="2" tip="不定时的弹跳。" />
			<body cnName="变身载具的血量" name="vehicleLife" 							min="0" 	max="1" fixed="2" tip="血量为百分之多少时变身载具。" />
		</father>	
		<father name="heroAI_attack" cnName="攻击属性">	
			<body cnName="不打无敌怪" name="awayInvincibleBuffB"  tip="不攻击无敌的敌人。" />
			<body cnName="不打抵御怪" name="noFoggyB" 			tip="不攻击带“抵御”技能的怪物。"/>
			<body cnName="爆头概率" name="shootHeadPro" 							min="0" 	max="1" fixed="2" tip="射击头部的概率" />
			<body cnName="根据射程切枪" name="chooseArmsByGapB" 			tip="综合考虑武器的射程与敌人的距离进行切换武器。"/>
			<body cnName="使用副手" name="weaponB" 			tip="是否使用副手；敌人拥有副手防御和利刃盾时不使用。"/>
		</father>
		<father name="heroAI_eacape" cnName="逃跑属性">
			<body cnName="是否逃跑" name="eacapeEnabled" 							 tip="开启这个功能后，才能设置下方的逃跑血量、逃跑隐身。"/>
			<body cnName="逃跑血量" name="eacapeLifeMul" 							min="0" 	max="1" fixed="2" tip="生命值低于多少时开始逃跑。" />
			<body cnName="逃跑隐身" name="eacapeAndHidingB"  tip="逃跑时是否开启隐身技能（如果有的话）。" />
		</father>
	</gather>
</data>