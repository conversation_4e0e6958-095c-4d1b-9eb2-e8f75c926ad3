<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="demBall" cnName="万能球" priceType="demBall" labelArr="all">
		<goods defineLabel="lightCone" price="5" 			buyLimitNum="60"/>
		<goods defineLabel="zodiacCash" price="6" 			buyLimitNum="200" />
		<goods defineLabel="yearCattle" price="4" 			buyLimitNum="70" />
		<goods defineLabel="yearMonkey" price="4" 			buyLimitNum="70" />
		<goods defineLabel="rocketCate" price="4" 			buyLimitNum="150" />
		
		<goods defineLabel="skeletonMedal_1" price="2" 			buyLimitNum="80" />
		<goods dataType="parts" defineLabel="huntParts_1" price="1" 		buyLimitNum="150"/>
		<goods dataType="parts" defineLabel="acidicParts_1" price="1" 	buyLimitNum="150"/>
		
		<goods defineLabel="fireGem" price="2" 				buyLimitNum="300" />
		<goods defineLabel="electricGem" price="2" 			buyLimitNum="300" />
		<goods defineLabel="frozenGem" price="2" 			buyLimitNum="300" />
		<goods defineLabel="poisonGem" price="2" 			buyLimitNum="300" />
		<goods defineLabel="alertGem" price="2" 			buyLimitNum="300" />
		<goods defineLabel="wisdomGem" price="2" 			buyLimitNum="300" />
		<goods defineLabel="agileGem" price="2" 				buyLimitNum="300" />
		<goods defineLabel="defenceGem" price="2" 			buyLimitNum="300" />
		
	</father>
</data>