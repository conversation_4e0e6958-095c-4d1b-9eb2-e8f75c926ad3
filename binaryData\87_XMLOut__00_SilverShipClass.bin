<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="SilverShip">
		<pro cnName="强力子弹" name="silverBulletHurt" skill="silverBulletHurt" max="11" />
		<pro cnName="全域光波" name="silverScreen" skill="silverScreen" max="11"/>
		<pro cnName="跟踪导弹" name="silverMissile" skill="silverMissile" max="11"/>
		<pro cnName="无敌护盾" name="silverShield" skill="silverShield" max="11"/>
		
	</father>
	<father name="craft">
		<craft name="SilverShip"/>
		<![CDATA[  强力子弹  ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇]]>		
		<skill>
			<name>silverBulletHurt</name>
			<cnName>强力子弹</cnName><ignoreSilenceB>1</ignoreSilenceB><ignoreNoSkillB>1</ignoreNoSkillB>
			<changeText>伤害提升：[mul-1]</changeText><everNoClearB>1</everNoClearB><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
			<iconUrl>SkillIcon/silverShotgun</iconUrl>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<addType>state</addType>
			<effectType>craftBulletHurtMul</effectType>
			<mul>1.1</mul>
			<duration>9999999</duration>
			<!--图像------------------------------------------------------------ -->
			<description>主炮伤害提升[mul-1]。</description>
			<!-- 技能各等级 ------------------------------------------------------------ -->
			<growth>
				<skill><mul>1.1</mul></skill>
				<skill><mul>1.2</mul></skill>
				<skill><mul>1.3</mul></skill>
				<skill><mul>1.4</mul></skill>
				<skill><mul>1.5</mul></skill>
				<skill><mul>1.6</mul></skill>
				<skill><mul>1.7</mul></skill>
				<skill><mul>1.8</mul></skill>
				<skill><mul>1.9</mul></skill>
				<skill><mul>2.0</mul></skill>
				<skill><mul>2.1</mul></skill>
			</growth>
		</skill>
		<![CDATA[  光波  ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇]]>
		<skill>
			<name>silverScreen</name>
			<cnName>全域光波</cnName>
			<changeText>冷却时间：[cd]秒</changeText>
			<cd>64</cd><delay>0.33</delay><ie>meFirst</ie><iconUrl>SkillIcon/silverScreen</iconUrl>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>beforeAttack</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			
			<addType>instant</addType>
			<effectType>bullet_silverScreen</effectType><effectFather>weShip</effectFather>
			<extraValueType>meDpsFactor</extraValueType>
			<mul>3</mul><!-- 伤害倍数 -->
			<!-- 子弹所需 -->
			<obj>"name":"silverScreenBullet","site":"meMid","flipB":false</obj>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg name="silverScreen"/>
			<description>释放两道全域光波，攻击在场的所有敌人！</description>
			<meActionLabel></meActionLabel>
			<!-- 技能各等级 ------------------------------------------------------------ -->
			<growth>
				<skill><cd>64</cd><firstCd>32</firstCd></skill>
				<skill><cd>55</cd><firstCd>27</firstCd></skill>
				<skill><cd>48</cd><firstCd>24</firstCd></skill>
				<skill><cd>42</cd><firstCd>21</firstCd></skill>
				<skill><cd>37</cd><firstCd>19</firstCd></skill>
				<skill><cd>33</cd><firstCd>16</firstCd></skill>
				<skill><cd>30</cd><firstCd>15</firstCd></skill>
				<skill><cd>27</cd><firstCd>14</firstCd></skill>
				<skill><cd>25</cd><firstCd>13</firstCd></skill>
				<skill><cd>24</cd><firstCd>12</firstCd></skill>
				<skill><cd>23</cd><firstCd>11</firstCd></skill>
			</growth>
		</skill>
					<bullet>
						<name>silverScreenBullet</name>
						<cnName>全域光波子弹</cnName>
						<hitType>longLine</hitType>
						<!--武器属性------------------------------------------------------------ -->
						<hurtRatio>1</hurtRatio><noMagneticB>1</noMagneticB><noBeClearB>1</noBeClearB>
						<bulletLife>5</bulletLife>
						<bulletWidth>2500</bulletWidth>
						<bulletSpeed>0</bulletSpeed>
						<bulletAngle>-90</bulletAngle>
						<bulletSkillArr>silverScreenBullet20</bulletSkillArr>
						<penetrationGap>10000</penetrationGap>
						<penetrationNum>999</penetrationNum>
						<oneHitBodyB>1</oneHitBodyB>
						<bulletImgUrl name="silverScreenBullet"/>
						<hitImgUrl name="silverScreenBulletHit"/>
					</bullet>
					
					<bullet>
						<name>silverScreenBulletHero</name>
						<cnName>全域光波子弹</cnName>
						<hitType>longLine</hitType>
						<!--武器属性------------------------------------------------------------ -->
						<hurtRatio>1</hurtRatio><noMagneticB>1</noMagneticB><noBeClearB>1</noBeClearB>
						<bulletLife>5</bulletLife>
						<bulletWidth>2500</bulletWidth>
						<bulletSpeed>0</bulletSpeed>
						<bulletAngle>-90</bulletAngle>
						<bulletSkillArr>silverScreenBullet20</bulletSkillArr>
						<skillArr>silverScreenHurt</skillArr>
						<penetrationGap>10000</penetrationGap>
						<penetrationNum>999</penetrationNum>
						<oneHitBodyB>1</oneHitBodyB>
						<bulletImgUrl name="silverScreenBullet"/>
						<hitImgUrl name="silverScreenBulletHit"/>
					</bullet>
					<skill>
						<name>silverScreenHurt</name>
						<cnName>全域光波-伤害</cnName><ignoreImmunityB>1</ignoreImmunityB><ignoreNoSkillB>1</ignoreNoSkillB>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>hit</condition>
						<target>target</target>
						<addType>instant</addType>
						<effectType>silverScreenHurt</effectType>
					</skill>
					
		<![CDATA[  跟踪导弹  ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇]]>		
		<skill>
			<name>silverMissile</name>
			<cnName>跟踪导弹</cnName><ignoreSilenceB>1</ignoreSilenceB><ignoreNoSkillB>1</ignoreNoSkillB>
			<changeText>发射间隔：[value]秒</changeText><everNoClearB>1</everNoClearB><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
			<iconUrl>SkillIcon/silverMissile</iconUrl>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>bodyAdd</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			
			<addType>state</addType>
			<effectType>bullet_silverMissile</effectType><effectFather>weShip</effectFather>
			<extraValueType>meDpsFactor</extraValueType>
			<mul>0.2</mul><!-- 伤害倍数 -->
			<value>1.1</value>
			<!-- 子弹所需 -->
			<obj>"name":"silverShipMissile","flipB":true,"site":"shootPoint"</obj>
			<duration>9999999</duration>
			<!--图像------------------------------------------------------------ -->
			<description>每隔[value]秒发射一颗跟踪导弹攻击敌人。</description>
			<!-- 技能各等级 ------------------------------------------------------------ -->
			<growth>
				<skill><value>1.10</value></skill>
				<skill><value>0.85</value></skill>
				<skill><value>0.67</value></skill>
				<skill><value>0.50</value></skill>
				<skill><value>0.40</value></skill>
				<skill><value>0.33</value></skill>
				<skill><value>0.29</value></skill>
				<skill><value>0.25</value></skill>
				<skill><value>0.22</value></skill>
				<skill><value>0.20</value></skill>
				<skill><value>0.18</value></skill>
				
				<![CDATA[
				<skill><value>1</value></skill>
				<skill><value>0.85</value></skill>
				<skill><value>0.72</value></skill>
				<skill><value>0.60</value></skill>
				<skill><value>0.50</value></skill>
				<skill><value>0.43</value></skill>
				<skill><value>0.38</value></skill>
				<skill><value>0.33</value></skill>
				<skill><value>0.30</value></skill>
				<skill><value>0.27</value></skill>
				]]>
			</growth>
		</skill>
					<bullet>
						<name>silverShipMissile</name><hurtRatio>1</hurtRatio> 
						<cnName>跟踪导弹</cnName>
						<shakeAngle>45</shakeAngle><bulletAngle>-180</bulletAngle>
						<bulletLife>2</bulletLife><bulletWidth>30</bulletWidth>
						<hitType>rect</hitType><bulletSpeed>20</bulletSpeed>
						<shootPoint>-26,18</shootPoint>
						<followD value="1" delay="0.3" />
						<speedD max="30" min="20" a="5" />
						<bulletImgUrl name="SilverShipMissile"/><smokeImgUrl name="SilverShipMissileSmoke"/><hitImgUrl name="smallSpace"/>
					</bullet>
					
					
		<![CDATA[  无敌护盾  ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇]]>
		<skill>
			<name>silverShield</name>
			<cnName>无敌护盾</cnName>
			<changeText>冷却时间：[cd]秒</changeText>
			<cd>80</cd><iconUrl>SkillIcon/silverShield</iconUrl>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType><isInvincibleB>1</isInvincibleB><isDefenceB>1</isDefenceB>
			<condition>underHit</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			
			<addType>state</addType>
			<effectType>reverseHurt</effectType>
			<value>0.4</value>
			<duration>6</duration>
			<!--图像------------------------------------------------------------ -->
			<targetEffectImg name="feedback_enemy_me"/>
			<stateEffectImg name="SaberTiger_shield_second_state"/>
			<description>生成无敌护盾，持续[duration]秒，期间会将受到伤害的40%转为飞船耐久。</description>
			<!-- 技能各等级 ------------------------------------------------------------ -->
			<growth>
				<skill><cd>90</cd></skill>
				<skill><cd>80</cd></skill>
				<skill><cd>72</cd></skill>
				<skill><cd>64</cd></skill>
				<skill><cd>57</cd></skill>
				<skill><cd>50</cd></skill>
				<skill><cd>45</cd></skill>
				<skill><cd>40</cd></skill>
				<skill><cd>38</cd></skill>
				<skill><cd>36</cd></skill>
				<skill><cd>34</cd></skill>
			</growth>
		</skill>
	</father>
	
	
	
	<![CDATA[  单位  ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇]]>
	<father name="craft">
		<body shell="compound">
			<name>SilverShip</name>
			<cnName>幻银</cnName>
			<raceType>ship</raceType><rosRatio>1</rosRatio>
			<swfUrl>swf/ship/SilverShip.swf</swfUrl><imgType>normal</imgType>
			<bmpUrl>BodyImg/SilverShip</bmpUrl>
			<!-- 图像 -->
			<dieImg name="stoneBoom"/>
			<dieJumpMul>0</dieJumpMul>
			<imgClass>CarImage</imgClass>
			
			<flipCtrlBy>mouse</flipCtrlBy>
			<rotateBySlopeB>1</rotateBySlopeB>
			<imgArr>
				stand,screenAttack,die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-20,-20,40,40</hitRect>
			<!-- 运动 -->
			<motionD F_AIR="8"/>
			<motionClass>AircraftGroundMotion</motionClass>
			<motionState>fly</motionState>
			<flyType>space</flyType>
			<maxVx>14</maxVx>
			<!-- 技能 -->
			<attackAIClass>CarAttack_AI</attackAIClass>
			<keyClass>CraftBodyKey</keyClass>
			<bulletLauncherClass>CraftBulletLauncher</bulletLauncherClass>
			<skillArr></skillArr>
			<vBullet type="main"  label="SilverShip_main" dpsMul="1" len="0" minRa="-180" maxRa="-179.99"/>
		</body>
		
		<body shell="compound">
			<name>SilverShipBig</name>
			<cnName>大幻银</cnName>
			<raceType>ship</raceType><rosRatio>1</rosRatio>
			<swfUrl>swf/ship/SilverShipBig.swf</swfUrl><imgType>normal</imgType>
			<bmpUrl>BodyImg/SilverShip</bmpUrl>
			<!-- 图像 -->
			<dieImg name="stoneBoom"/>
			<dieJumpMul>0</dieJumpMul>
			<imgClass>CarImage</imgClass>
			
			<flipCtrlBy>mouse</flipCtrlBy>
			<rotateBySlopeB>1</rotateBySlopeB>
			<imgArr>
				stand,die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-20,-20,40,40</hitRect>
			<!-- 运动 -->
			<motionD F_AIR="8"/>
			<motionClass>AircraftGroundMotion</motionClass>
			<motionState>fly</motionState>
			<flyType>space</flyType>
			<maxVx>14</maxVx>
			<!-- 技能 -->
			<attackAIClass>CarAttack_AI</attackAIClass>
			<keyClass>CraftBodyKey</keyClass>
			<bulletLauncherClass>CraftBulletLauncher</bulletLauncherClass>
			<skillArr></skillArr>
			<vBullet type="main"  label="SilverShip_main" dpsMul="1" len="0" minRa="-180" maxRa="-179.99"/>
		</body>
		
		<![CDATA[  主炮  ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇]]>
		<bullet>
			<name>SilverShip_main</name><hurtRatio>0.1</hurtRatio> 
			<cnName>幻银主炮0级</cnName>
			<bulletLife>0.65</bulletLife><bulletWidth>30</bulletWidth><extendGap>20</extendGap>
			<hitType>rect</hitType><attackGap>0.1</attackGap><screenShakeValue>4</screenShakeValue><bulletSpeed>40</bulletSpeed>
			<bulletImgUrl name="SilverShipBullet"/><fireImgUrl name="SilverShipFire"/><hitImgUrl name="gun_hit"/>
		</bullet>
		<![CDATA[
		<bullet>
			<name>SilverShip_main1</name><hurtRatio>0.06</hurtRatio><bulletNum>2</bulletNum><shootAngle>2</shootAngle>
			<cnName>幻银主炮1级</cnName>
			<bulletLife>0.65</bulletLife><bulletWidth>30</bulletWidth><extendGap>20</extendGap>
			<hitType>rect</hitType><attackGap>0.10</attackGap><screenShakeValue>4</screenShakeValue><bulletSpeed>40</bulletSpeed>
			<bulletImgUrl name="SilverShipBullet"/><fireImgUrl name="SilverShipFire"/><hitImgUrl name="gun_hit"/>
		</bullet>
		<bullet>
			<name>SilverShip_main2</name><hurtRatio>0.047</hurtRatio><bulletNum>3</bulletNum><shootAngle>3</shootAngle>
			<cnName>幻银主炮2级</cnName>
			<bulletLife>0.65</bulletLife><bulletWidth>30</bulletWidth><extendGap>20</extendGap>
			<hitType>rect</hitType><attackGap>0.10</attackGap><screenShakeValue>4</screenShakeValue><bulletSpeed>40</bulletSpeed>
			<bulletImgUrl name="SilverShipBullet"/><fireImgUrl name="SilverShipFire"/><hitImgUrl name="gun_hit"/>
		</bullet>
		<bullet>
			<name>SilverShip_main3</name><hurtRatio>0.040</hurtRatio><bulletNum>4</bulletNum><shootAngle>4</shootAngle>
			<cnName>幻银主炮3级</cnName>
			<bulletLife>0.65</bulletLife><bulletWidth>30</bulletWidth><extendGap>20</extendGap>
			<hitType>rect</hitType><attackGap>0.10</attackGap><screenShakeValue>4</screenShakeValue><bulletSpeed>40</bulletSpeed>
			<bulletImgUrl name="SilverShipBullet"/><fireImgUrl name="SilverShipFire"/><hitImgUrl name="gun_hit"/>
		</bullet>
		<bullet>
			<name>SilverShip_main4</name><hurtRatio>0.036</hurtRatio><bulletNum>5</bulletNum><shootAngle>5</shootAngle>
			<cnName>幻银主炮4级</cnName>
			<bulletLife>0.65</bulletLife><bulletWidth>30</bulletWidth><extendGap>20</extendGap>
			<hitType>rect</hitType><attackGap>0.10</attackGap><screenShakeValue>4</screenShakeValue><bulletSpeed>40</bulletSpeed>
			<bulletImgUrl name="SilverShipBullet"/><fireImgUrl name="SilverShipFire"/><hitImgUrl name="gun_hit"/>
		</bullet>
		<bullet>
			<name>SilverShip_main5</name><hurtRatio>0.034</hurtRatio><bulletNum>6</bulletNum><shootAngle>6</shootAngle>
			<cnName>幻银主炮5级</cnName>
			<bulletLife>0.65</bulletLife><bulletWidth>30</bulletWidth><extendGap>20</extendGap>
			<hitType>rect</hitType><attackGap>0.10</attackGap><screenShakeValue>4</screenShakeValue><bulletSpeed>40</bulletSpeed>
			<bulletImgUrl name="SilverShipBullet"/><fireImgUrl name="SilverShipFire"/><hitImgUrl name="gun_hit"/>
		</bullet>
		]]>
		
		
	</father>
</data>